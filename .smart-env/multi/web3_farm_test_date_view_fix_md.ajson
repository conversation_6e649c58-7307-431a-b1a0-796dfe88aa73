
"smart_sources:web3/farm/test_date_view_fix.md": {"path":"web3/farm/test_date_view_fix.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06669027,-0.0273955,0.00975932,-0.0196738,0.03356382,0.00419118,-0.06467605,-0.05968538,0.02952408,0.0368699,0.05408641,-0.09440638,0.044827,0.06359923,0.00685462,0.0094995,-0.05914199,-0.01020143,-0.04889404,-0.00558349,0.07461993,-0.00009041,-0.00966985,-0.02274568,0.06863264,0.07019069,-0.01257771,-0.04984922,-0.00163379,-0.15346305,-0.00417068,-0.03237249,0.04609515,0.03270847,0.02773215,-0.03403328,0.04361981,0.03440128,-0.05073833,0.02626763,0.03417967,-0.00536232,0.01040294,-0.03446676,-0.0530869,-0.02823864,0.0110334,0.01230165,0.02487795,-0.02642694,-0.00595686,-0.08672662,-0.00639791,-0.00147506,0.01864735,-0.0062932,0.01989857,0.0921672,0.03332905,0.03165504,0.07105304,0.03027054,-0.21053402,0.04968386,0.0369994,-0.01564407,-0.0153076,-0.02863319,0.01945361,0.03772696,-0.04809621,0.03342021,-0.00753086,0.02930964,-0.01514169,-0.00972349,0.03797562,-0.00751627,0.00600005,-0.03565392,-0.04034523,0.08908248,-0.00487437,0.03328296,0.03131241,0.01430489,-0.02237475,-0.00217627,0.01116963,0.02015784,-0.03772089,-0.04328581,-0.04423869,0.02067247,-0.01503262,0.01250744,0.00532689,0.02795844,-0.09563779,0.12083126,-0.05674124,0.03939467,-0.02380542,-0.04183528,0.02327808,-0.02259349,-0.10111897,-0.0536533,-0.01766232,-0.0111623,-0.04596059,0.04559413,0.06807875,-0.05047577,-0.00531146,0.04752639,0.05158367,-0.01712905,0.01238934,0.02132407,0.07654436,0.04364535,0.04275793,-0.02421042,0.00493827,-0.01211255,0.07467379,0.07602743,-0.05198291,0.03133764,0.04790594,-0.02821444,-0.10666733,-0.02300942,-0.02864625,0.0166487,0.00275643,-0.01374605,-0.00183766,-0.07754843,-0.0475866,-0.0955531,0.00046084,-0.05858596,-0.08972242,0.09665603,-0.00661343,0.03694141,0.01317867,-0.12084096,-0.02282762,0.09204976,-0.0098843,-0.04843918,0.02756301,0.04718328,0.03452756,0.07460933,-0.0516333,0.00809747,-0.06870408,0.02516874,-0.09038263,0.06193104,0.08134489,-0.08962853,0.01065642,0.03967569,0.02525097,-0.02320204,-0.00044291,0.03143445,-0.02804834,-0.06781546,0.10079964,-0.01057126,0.03923417,0.02538862,0.01231965,0.05081329,0.04733912,-0.03339796,-0.05965752,0.05252739,-0.0344811,-0.04919614,0.02732224,-0.04117084,0.0344048,-0.02698016,-0.04563375,-0.02741087,0.00140095,0.01991148,-0.02729026,-0.00350468,-0.01518742,0.03267226,0.05231383,-0.06091403,0.16204023,0.0015379,-0.00154033,-0.03049859,-0.02174421,0.02011698,0.00802385,-0.05867756,0.01033367,0.05961947,-0.02729576,0.04437381,0.00713251,0.02242615,-0.00028299,0.04772287,0.00310546,0.03859279,-0.00375443,0.05327668,0.01886626,0.00702584,-0.02403982,-0.23263112,-0.04813503,0.00263654,-0.04630848,-0.00101765,0.01275569,0.00850966,-0.03847701,0.09553885,0.06753061,0.09717724,0.07885507,-0.0456671,-0.01758492,-0.02274896,-0.00366974,0.03511507,-0.00174733,-0.0301024,-0.01290155,-0.02741393,0.0016554,0.02658577,-0.08271729,0.04541578,-0.04710431,0.11661099,-0.01419832,0.02478568,-0.02296714,0.07764341,0.00898538,-0.06369945,-0.08952521,0.0274141,0.01391789,-0.03163034,-0.03773193,-0.06634383,-0.04444841,-0.01438159,0.00714112,0.02506344,-0.09041012,-0.02038162,-0.01196337,0.02747821,-0.02974395,0.00088357,0.02029017,0.00334194,0.02192024,0.05664502,0.08839259,0.06580917,-0.00990371,-0.02235431,0.0342905,-0.01500923,-0.00144804,0.02677111,0.00181907,0.00618489,0.04707592,-0.00058714,-0.03544962,-0.00348985,-0.04115369,0.00701782,0.03012609,-0.04820441,0.10098292,-0.0377222,-0.0243333,0.02802496,-0.01798714,-0.03193862,-0.05091773,0.05724695,-0.02810399,-0.01220993,-0.01901348,0.04353509,0.02559246,0.06449977,0.05146375,0.01755443,-0.00261411,0.04168034,0.02143697,-0.04313946,-0.04281044,-0.06077738,-0.0444087,0.10063501,-0.00429907,-0.30119446,0.01667274,-0.00251902,-0.00340948,0.03487105,-0.03249852,-0.00331418,-0.02519674,-0.06310683,-0.04360377,-0.02635418,0.08646394,0.03678962,-0.08877644,0.0069893,0.00119686,0.00075548,-0.01612634,0.04580951,-0.01111048,0.01522188,0.06858779,0.18555622,0.01439076,0.07783836,0.03267752,0.00757736,-0.02540632,0.08681309,0.01842311,-0.00710998,-0.00764542,0.07896778,0.00262707,0.02659483,0.04602233,-0.00180587,0.0032461,-0.01646761,0.01943604,-0.04742255,0.03140714,-0.04642195,0.02893665,0.11755146,0.0408417,-0.01761575,-0.04951335,-0.00717603,-0.00958552,-0.05363107,-0.0626009,-0.007688,0.01145232,-0.02766557,0.05630472,0.05226089,-0.03636109,-0.09167995,0.03512792,-0.00582225,-0.02832816,0.02358491,0.03491959,0.01159593],"last_embed":{"hash":"j09te1","tokens":386}}},"last_read":{"hash":"j09te1","at":1752156127300},"class_name":"SmartSource","last_import":{"mtime":1752156113197,"size":4492,"at":1752156127142,"hash":"j09te1"},"blocks":{"#日期视图修复测试":[1,120],"#日期视图修复测试#问题描述":[3,8],"#日期视图修复测试#问题描述#{1}":[5,5],"#日期视图修复测试#问题描述#{2}":[6,6],"#日期视图修复测试#问题描述#{3}":[7,8],"#日期视图修复测试#问题根源分析":[9,23],"#日期视图修复测试#问题根源分析#原始问题":[11,16],"#日期视图修复测试#问题根源分析#原始问题#{1}":[12,12],"#日期视图修复测试#问题根源分析#原始问题#{2}":[13,13],"#日期视图修复测试#问题根源分析#原始问题#{3}":[14,14],"#日期视图修复测试#问题根源分析#原始问题#{4}":[15,16],"#日期视图修复测试#问题根源分析#修复方案":[17,23],"#日期视图修复测试#问题根源分析#修复方案#{1}":[18,18],"#日期视图修复测试#问题根源分析#修复方案#{2}":[19,23],"#日期视图修复测试#修改内容":[24,45],"#日期视图修复测试#修改内容#1. 新增 calculateInvestDaysForDateView 函数":[26,35],"#日期视图修复测试#修改内容#1. 新增 calculateInvestDaysForDateView 函数#{1}":[27,35],"#日期视图修复测试#修改内容#2. 修改 processProjectDateEntry 函数":[36,41],"#日期视图修复测试#修改内容#2. 修改 processProjectDateEntry 函数#{1}":[37,37],"#日期视图修复测试#修改内容#2. 修改 processProjectDateEntry 函数#{2}":[38,38],"#日期视图修复测试#修改内容#2. 修改 processProjectDateEntry 函数#{3}":[39,39],"#日期视图修复测试#修改内容#2. 修改 processProjectDateEntry 函数#{4}":[40,41],"#日期视图修复测试#修改内容#3. 增强错误处理":[42,45],"#日期视图修复测试#修改内容#3. 增强错误处理#{1}":[43,43],"#日期视图修复测试#修改内容#3. 增强错误处理#{2}":[44,45],"#日期视图修复测试#预期效果":[46,52],"#日期视图修复测试#预期效果#{1}":[48,48],"#日期视图修复测试#预期效果#{2}":[49,49],"#日期视图修复测试#预期效果#{3}":[50,50],"#日期视图修复测试#预期效果#{4}":[51,52],"#日期视图修复测试#测试验证":[53,60],"#日期视图修复测试#测试验证#{1}":[55,55],"#日期视图修复测试#测试验证#{2}":[56,56],"#日期视图修复测试#测试验证#{3}":[57,57],"#日期视图修复测试#测试验证#{4}":[58,58],"#日期视图修复测试#测试验证#{5}":[59,60],"#日期视图修复测试#技术细节":[61,72],"#日期视图修复测试#技术细节#投资天数计算原则":[63,66],"#日期视图修复测试#技术细节#投资天数计算原则#{1}":[64,64],"#日期视图修复测试#技术细节#投资天数计算原则#{2}":[65,66],"#日期视图修复测试#技术细节#USDT金额计算原则":[67,72],"#日期视图修复测试#技术细节#USDT金额计算原则#{1}":[68,68],"#日期视图修复测试#技术细节#USDT金额计算原则#{2}":[69,70],"#日期视图修复测试#技术细节#USDT金额计算原则#{3}":[71,72],"#日期视图修复测试#修复完成状态":[73,84],"#日期视图修复测试#修复完成状态#{1}":[75,75],"#日期视图修复测试#修复完成状态#{2}":[76,76],"#日期视图修复测试#修复完成状态#{3}":[77,77],"#日期视图修复测试#修复完成状态#{4}":[78,79],"#日期视图修复测试#修复完成状态#{5}":[80,80],"#日期视图修复测试#修复完成状态#{6}":[81,81],"#日期视图修复测试#修复完成状态#{7}":[82,82],"#日期视图修复测试#修复完成状态#{8}":[83,84],"#日期视图修复测试#代码变更详情":[85,102],"#日期视图修复测试#代码变更详情#变更1：新增投资天数计算函数":[87,90],"#日期视图修复测试#代码变更详情#变更1：新增投资天数计算函数#{1}":[88,90],"#日期视图修复测试#代码变更详情#变更2：重构项目日期条目处理函数":[91,98],"#日期视图修复测试#代码变更详情#变更2：重构项目日期条目处理函数#{1}":[92,93],"#日期视图修复测试#代码变更详情#变更2：重构项目日期条目处理函数#{2}":[94,94],"#日期视图修复测试#代码变更详情#变更2：重构项目日期条目处理函数#{3}":[95,95],"#日期视图修复测试#代码变更详情#变更2：重构项目日期条目处理函数#{4}":[96,96],"#日期视图修复测试#代码变更详情#变更2：重构项目日期条目处理函数#{5}":[97,98],"#日期视图修复测试#代码变更详情#变更3：增强调用处的错误处理":[99,102],"#日期视图修复测试#代码变更详情#变更3：增强调用处的错误处理#{1}":[100,102],"#日期视图修复测试#技术实现亮点":[103,109],"#日期视图修复测试#技术实现亮点#{1}":[105,105],"#日期视图修复测试#技术实现亮点#{2}":[106,106],"#日期视图修复测试#技术实现亮点#{3}":[107,107],"#日期视图修复测试#技术实现亮点#{4}":[108,109],"#日期视图修复测试#验证建议":[110,120],"#日期视图修复测试#验证建议#{1}":[112,112],"#日期视图修复测试#验证建议#{2}":[113,113],"#日期视图修复测试#验证建议#{3}":[114,114],"#日期视图修复测试#验证建议#{4}":[115,115],"#日期视图修复测试#验证建议#{5}":[116,116],"#日期视图修复测试#验证建议#{6}":[117,118],"#日期视图修复测试#验证建议#{7}":[119,120]},"outlinks":[],"last_embed":{"hash":"j09te1","at":1752156127144}},