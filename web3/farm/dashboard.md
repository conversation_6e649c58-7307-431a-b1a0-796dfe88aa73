
```dataviewjs
// ===== Web3 Farm Dashboard 仪表板 =====
//
// 🚨 重要提醒：在修改此代码或进行开发之前，请务必先阅读 README_dashboard.md 文档！
//
// 该文档包含：
// - 完整的代码架构说明和模块化设计
// - 详细的函数说明和计算逻辑
// - 开发者修改指引和影响分析
// - 常见问题排查和调试建议
// - 高风险修改警告和最佳实践
//
// 文档路径：web3/farm/README_dashboard.md
//
// ===== 初始化全局变量 ===== (静默执行)
// 清空当前输出
dv.container.innerHTML = '';

window.DashboardConfig = {
    HISTORY_PATH: '"web3/farm/history"',
    RISK_LIMIT_PATH: 'web3/farm/data/risk_percentage',
    PRICES_FILE_PATH: 'web3/farm/data/token_prices',
    LEVEL_LIMIT_PATH: 'web3/farm/data/invest_level_limit'
};

// ===== 表格渲染工具 =====
window.DashboardTableRenderer = {
    // 处理单元格内容，清理Markdown格式并处理链接
    processCellContent: (cell, columnIndex, headers) => {
        if (!cell || cell === '') return '';

        // 检查是否是Dataview链接对象
        if (cell && typeof cell === 'object' && cell.path) {
            // 这是一个Dataview链接对象
            const fileName = cell.path.split('/').pop().replace('.md', '') || cell.path;
            return `<a href="obsidian://open?file=${encodeURIComponent(cell.path)}" style="color: var(--link-color); text-decoration: underline;">${fileName}</a>`;
        }

        let content = cell.toString();

        // 如果内容已经包含HTML标签，直接返回（避免重复处理）
        if (content.includes('<span') || content.includes('<strong') || content.includes('<a')) {
            return content;
        }

        // 清理Markdown格式的粗体标记，转换为HTML
        content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

        // 处理项目列的链接（通常是第5列，索引为4）
        const projectColumnIndex = headers.findIndex(h => h.includes('项目'));
        if (columnIndex === projectColumnIndex && content.includes('[[') && content.includes(']]')) {
            // 提取链接路径和显示文本
            const linkMatch = content.match(/\[\[(.*?)\]\]/);
            if (linkMatch) {
                const linkPath = linkMatch[1];
                const fileName = linkPath.split('/').pop().replace('.md', '') || linkPath;
                // 创建Obsidian内部链接
                content = `<a href="obsidian://open?file=${encodeURIComponent(linkPath)}" style="color: var(--link-color); text-decoration: underline;">${fileName}</a>`;
            }
        }

        // 处理其他可能的链接格式
        if (content.includes('[[') && content.includes(']]') && !content.includes('<a')) {
            content = content.replace(/\[\[(.*?)\]\]/g, (match, linkPath) => {
                const fileName = linkPath.split('/').pop().replace('.md', '') || linkPath;
                return `<a href="obsidian://open?file=${encodeURIComponent(linkPath)}" style="color: var(--link-color); text-decoration: underline;">${fileName}</a>`;
            });
        }

        return content;
    },

    // 渲染带固定表头和第一列的表格
    renderFixedTable: (headers, data, containerId = 'dashboard-table') => {
        let tableHTML = `
            <div id="${containerId}" class="dashboard-table-container" style="position: relative; overflow: auto; max-height: 80vh; border: 1px solid var(--background-modifier-border); border-radius: 6px;">
                <table class="dashboard-fixed-table" style="width: 100%; border-collapse: collapse; font-size: 14px;">
                    <thead style="position: sticky; top: 0; z-index: 10; background-color: var(--background-primary);">
                        <tr>`;

        // 渲染表头
        headers.forEach((header, index) => {
            const isFirstColumn = index === 0;
            const stickyStyle = isFirstColumn ?
                'position: sticky; left: 0; z-index: 15; background-color: var(--background-primary); border-right: 2px solid var(--background-modifier-border); min-width: 120px;' : '';
            tableHTML += `<th style="background-color: var(--background-primary); border: 1px solid var(--background-modifier-border); padding: 8px 12px; font-weight: bold; white-space: nowrap; ${stickyStyle}">${header}</th>`;
        });

        tableHTML += `</tr></thead><tbody>`;

        // 渲染数据行
        data.forEach((row, rowIndex) => {
            tableHTML += `<tr style="border-bottom: 1px solid var(--background-modifier-border);" onmouseover="this.style.backgroundColor='var(--background-secondary)'; this.querySelectorAll('td:first-child').forEach(td => td.style.backgroundColor='var(--background-secondary)');" onmouseout="this.style.backgroundColor=''; this.querySelectorAll('td:first-child').forEach(td => td.style.backgroundColor='var(--background-primary)');">`;
            row.forEach((cell, cellIndex) => {
                const isFirstColumn = cellIndex === 0;
                const stickyStyle = isFirstColumn ?
                    'position: sticky; left: 0; z-index: 5; background-color: var(--background-primary); border-right: 2px solid var(--background-modifier-border); font-weight: bold; min-width: 120px;' : '';

                // 处理单元格内容
                const processedContent = window.DashboardTableRenderer.processCellContent(cell, cellIndex, headers);

                tableHTML += `<td style="border: 1px solid var(--background-modifier-border); padding: 6px 10px; white-space: nowrap; ${stickyStyle}">${processedContent}</td>`;
            });
            tableHTML += `</tr>`;
        });

        tableHTML += `</tbody></table></div>`;

        return tableHTML;
    }
};

// ===== 视图状态管理 =====
window.DashboardViewState = {
    mainView: 'risk', // 'risk' | 'date'
    dateGranularity: 'monthly', // 'daily' | 'weekly' | 'monthly' | 'yearly'
    currentPage: 1
};

// ===== 公共工具模块 =====
window.DashboardUtils = {
    // 日期处理工具
    parseDate: (dateValue) => {
        if (dateValue instanceof Date) return dateValue;
        const dateStr = dateValue.toString().trim();

        // 处理纯数字日期格式 (如: 20250603)
        if (/^\d{8}/.test(dateStr)) {
            const match = dateStr.match(/^(\d{4})(\d{2})(\d{2})/);
            if (match) {
                const [, year, month, day] = match;
                return dv.date(`${year}-${month}-${day}`);
            }
        }

        // 其他格式直接使用 dv.date()
        return dv.date(dateStr);
    },

    // 字符串处理工具
    formatNumber: (num, decimals = 2) => {
        return num.toLocaleString(undefined, {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        });
    },

    formatPercentage: (value, decimals = 2) => {
        return value !== 0 ? `${value.toFixed(decimals)}%` : "0%";
    },

    // 风险等级颜色显示
    getRiskColoredText: (riskLevel, riskText) => {
        const risk = parseInt(riskLevel);
        let color = "#000000"; // 默认黑色

        if (risk <= 1) {
            color = "#28a745"; // 绿色：无风险、低风险
        } else if (risk === 2) {
            color = "#ffc107"; // 黄色：中风险
        } else if (risk >= 3) {
            color = "#dc3545"; // 红色：高风险、极高风险
        }

        return `<span style="color: ${color}; font-weight: bold;">${riskText}</span>`;
    },

    // APR Gap 颜色显示
    getAPRGapColoredText: (gapValue) => {
        const gap = parseFloat(gapValue);
        const formattedValue = window.DashboardUtils.formatPercentage(gap);

        if (gap >= 10) {
            return `<span style="color: #28a745;">${formattedValue}</span>`; // 绿色：gap >= 10%
        } else if (gap >= -10) {
            return formattedValue; // 正常范围：使用默认颜色，不设置样式
        } else if (gap >= -20) {
            return `<span style="color: #ffc107;">${formattedValue}</span>`; // 黄色：-20% <= gap < -10%
        } else {
            return `<span style="color: #dc3545;">${formattedValue}</span>`; // 红色：gap < -20%
        }
    },

    // 正负数颜色格式化（数字）
    formatColoredNumber: (num, decimals = 2) => {
        const value = parseFloat(num);
        const formattedValue = window.DashboardUtils.formatNumber(value, decimals);

        if (value > 0) {
            return `<span style="color: #28a745;">${formattedValue}</span>`; // 绿色：正数
        } else if (value < 0) {
            return `<span style="color: #dc3545;">${formattedValue}</span>`; // 红色：负数
        } else {
            return formattedValue; // 零值：默认颜色
        }
    },

    // 正负数颜色格式化（百分比）
    formatColoredPercentage: (value, decimals = 2) => {
        const numValue = parseFloat(value);
        const formattedValue = window.DashboardUtils.formatPercentage(numValue, decimals);

        if (numValue > 0) {
            return `<span style="color: #28a745;">${formattedValue}</span>`; // 绿色：正数
        } else if (numValue < 0) {
            return `<span style="color: #dc3545;">${formattedValue}</span>`; // 红色：负数
        } else {
            return formattedValue; // 零值：默认颜色
        }
    },

    // 项目状态颜色格式化
    formatProjectStatus: (status) => {
        const statusText = status || "Doing";

        if (statusText === "Doing") {
            return `<span style="color: #28a745;">🟢 ${statusText}</span>`; // 绿色圆点 + Doing
        } else if (statusText === "Done") {
            return `<span style="color: #6c757d;">⚪ ${statusText}</span>`; // 灰色圆点 + Done
        } else {
            return statusText; // 其他状态：默认显示
        }
    }
};

// 模块1加载完成（静默）
```
```dataviewjs
// ===== 数据加载模块 ===== (静默执行)
// 清空当前输出
dv.container.innerHTML = '';

// ===== 数据加载模块 =====
window.DashboardDataLoader = {
    // 解析风险配置文件
    parseRiskFile: (path) => {
        const page = dv.page(path);
        if (!page || !page.file || !page.file.lists) return null;
        const limits = {};
        for (const item of page.file.lists) {
            if (item.risk !== undefined) {
                const riskLabel = item.cn ? item.cn.trim() : `风险 ${item.risk}`;
                const percentLimit = item.percent ? parseFloat(item.percent) : 0;
                const expectApr = item.expect_apr ? parseFloat(item.expect_apr) : 0;
                limits[item.risk] = { text: riskLabel, percent: percentLimit, expectApr: expectApr };
            }
        }
        return limits;
    },

    // 解析投资等级配置文件
    parseLevelFile: (path) => {
        const page = dv.page(path);
        if (!page || !page.file || !page.file.lists) return null;
        const limits = {};
        for (const item of page.file.lists) {
            if (item.level !== undefined) {
                const levelLabel = item.cn ? item.cn.trim() : `仓位 ${item.level}`;
                const limitAmount = item.limit ? parseFloat(item.limit) : 0;
                limits[item.level] = { text: levelLabel, limit: limitAmount, cn: levelLabel };
            }
        }
        return limits;
    },

    // 解析价格数据
    parsePriceData: (pricesPage) => {
        const priceMap = new Map();
        if (!pricesPage || !pricesPage.file.lists) return priceMap;

        for (const item of pricesPage.file.lists) {
            let token = item.token ? item.token.trim().toUpperCase() : undefined;
            let date = item.date ? dv.date(item.date).toFormat("yyyy-MM-dd") : undefined;
            let price = item.price ? parseFloat(item.price) : undefined;

            // Fallback to regex parsing if direct properties are not available
            if (token === undefined || date === undefined || price === undefined) {
                const tokenMatch = item.text.match(/token:\s*(.*)/);
                const dateMatch = item.text.match(/date:\s*(.*)/);
                const priceMatch = item.text.match(/price:\s*(.*)/);

                token = tokenMatch ? tokenMatch[1].trim().toUpperCase() : token;
                date = dateMatch ? dv.date(dateMatch[1].trim()).toFormat("yyyy-MM-dd") : date;
                price = priceMatch ? parseFloat(priceMatch[1].trim()) : price;
            }

            if (token && date && price != null) {
                if (!priceMap.has(token)) priceMap.set(token, new Map());
                priceMap.get(token).set(date, price);
            }
        }

        // 确保稳定币价格映射存在
        ['USDT', 'USDC', 'DAI'].forEach(s => {
            if (!priceMap.has(s)) priceMap.set(s, new Map());
        });

        return priceMap;
    },

    // 加载所有基础数据
    loadAllData: () => {
        const riskInfo = window.DashboardDataLoader.parseRiskFile(window.DashboardConfig.RISK_LIMIT_PATH);
        const levelInfo = window.DashboardDataLoader.parseLevelFile(window.DashboardConfig.LEVEL_LIMIT_PATH);
        const pricesPage = dv.page(window.DashboardConfig.PRICES_FILE_PATH);
        const priceMap = window.DashboardDataLoader.parsePriceData(pricesPage);
        const doingProjects = dv.pages(window.DashboardConfig.HISTORY_PATH).where(p => p.Status === "Doing");
        const allProjects = dv.pages(window.DashboardConfig.HISTORY_PATH); // 加载所有项目供日期视图使用

        return { riskInfo, levelInfo, priceMap, doingProjects, allProjects };
    }
};

// 模块2加载完成（静默）
```
```dataviewjs
// ===== 时间数据聚合模块 ===== (静默执行)
// 清空当前输出
dv.container.innerHTML = '';

// ===== 时间数据聚合模块 =====
window.DashboardDateAggregator = {
    // 按项目提取日期数据
    extractProjectDateData: (projects) => {
        const projectDateData = [];

        for (const project of projects) {
            if (!project.file || !project.file.lists || !Array.isArray(project.file.lists)) {
                continue;
            }

            const balanceEntries = project.file.lists.filter(item =>
                item &&
                item.balance !== undefined &&
                item.date !== undefined &&
                item.date !== null &&
                item.date.toString().trim() !== ""
            );

            if (balanceEntries.length === 0) continue;

            // 按日期排序
            balanceEntries.sort((a, b) => {
                try {
                    const dateA = window.DashboardUtils.parseDate(a.date);
                    const dateB = window.DashboardUtils.parseDate(b.date);
                    if (!dateA || !dateB) return 0;
                    return dateA.toMillis() - dateB.toMillis();
                } catch (e) {
                    return 0;
                }
            });

            for (const entry of balanceEntries) {
                try {
                    const date = window.DashboardUtils.parseDate(entry.date);
                    if (date) {
                        projectDateData.push({
                            project: project,
                            date: date.toFormat("yyyy-MM-dd"),
                            entry: entry,
                            dateObj: date
                        });
                    }
                } catch (e) {
                    // 忽略无效日期
                }
            }
        }

        return projectDateData.sort((a, b) => b.date.localeCompare(a.date)); // 按日期倒序排列
    },



    // 按月聚合项目日期数据
    aggregateByMonth: (projectDateData) => {
        const monthGroups = new Map();

        for (const item of projectDateData) {
            const monthKey = item.dateObj.toFormat("yyyy-MM");

            if (!monthGroups.has(monthKey)) {
                monthGroups.set(monthKey, {
                    monthKey: monthKey,
                    displayText: item.dateObj.toFormat("yyyy年M月"),
                    projectData: []
                });
            }

            monthGroups.get(monthKey).projectData.push(item);
        }

        // 转换为数组并按月份倒序排列
        return Array.from(monthGroups.values()).sort((a, b) => b.monthKey.localeCompare(a.monthKey));
    },

    // 按周聚合项目日期数据
    aggregateByWeek: (projectDateData) => {
        const weekGroups = new Map();

        for (const item of projectDateData) {
            const date = item.dateObj;

            // 计算ISO周数（周一为一周开始）
            const startOfYear = dv.date(`${date.year}-01-01`);
            const dayOfYear = Math.floor((date.toMillis() - startOfYear.toMillis()) / (1000 * 60 * 60 * 24)) + 1;
            const weekNumber = Math.ceil(dayOfYear / 7);

            // 计算周的开始和结束日期
            const weekStart = date.minus({ days: date.weekday - 1 });
            const weekEnd = weekStart.plus({ days: 6 });

            const weekKey = `${date.year}-W${weekNumber.toString().padStart(2, '0')}`;

            if (!weekGroups.has(weekKey)) {
                weekGroups.set(weekKey, {
                    weekKey: weekKey,
                    displayText: `${date.year}年第${weekNumber}周 (${weekStart.toFormat("M/d")}-${weekEnd.toFormat("M/d")})`,
                    weekStart: weekStart.toFormat("yyyy-MM-dd"),
                    weekEnd: weekEnd.toFormat("yyyy-MM-dd"),
                    projectData: []
                });
            }

            weekGroups.get(weekKey).projectData.push(item);
        }

        return Array.from(weekGroups.values()).sort((a, b) => b.weekKey.localeCompare(a.weekKey));
    },

    // 按年聚合项目日期数据
    aggregateByYear: (projectDateData) => {
        const yearGroups = new Map();

        for (const item of projectDateData) {
            const yearKey = item.dateObj.year.toString();

            if (!yearGroups.has(yearKey)) {
                yearGroups.set(yearKey, {
                    yearKey: yearKey,
                    displayText: `${yearKey}年`,
                    projectData: []
                });
            }

            yearGroups.get(yearKey).projectData.push(item);
        }

        return Array.from(yearGroups.values()).sort((a, b) => parseInt(b.yearKey) - parseInt(a.yearKey));
    }
};

// 模块2A时间聚合加载完成（静默）
```
```dataviewjs
// ===== 基础指标计算模块 ===== (静默执行)
// 清空当前输出
dv.container.innerHTML = '';

// ===== 基础指标计算模块 =====
window.DashboardBaseCalculator = {
    // 计算项目的历史记录数据
    calculateProjectHistory: (project, priceMap) => {
        if (!project.file || !project.file.lists || !Array.isArray(project.file.lists)) {
            return null;
        }

        const balanceEntries = project.file.lists.filter(item =>
            item &&
            item.balance !== undefined &&
            item.date !== undefined &&
            item.date !== null &&
            item.date.toString().trim() !== ""
        );

        if (balanceEntries.length === 0) return null;

        // 按日期排序
        balanceEntries.sort((a, b) => {
            try {
                const dateA = window.DashboardUtils.parseDate(a.date);
                const dateB = window.DashboardUtils.parseDate(b.date);
                if (!dateA || !dateB) return 0;
                return dateA.toMillis() - dateB.toMillis();
            } catch (e) {
                return 0;
            }
        });

        const firstEntry = balanceEntries[0];
        const lastEntry = balanceEntries.length === 1 ? balanceEntries[0] : balanceEntries[balanceEntries.length - 1];

        // 计算历史上所有add属性的总和
        let totalAddAmount = 0;
        try {
            for (const entry of balanceEntries) {
                if (entry && entry.add !== undefined && entry.add !== null) {
                    const addValue = parseFloat(entry.add);
                    if (!isNaN(addValue)) {
                        totalAddAmount += addValue;
                    }
                }
            }
        } catch (e) {
            totalAddAmount = 0;
        }

        const firstBalance = parseFloat(firstEntry.balance) || 0;
        const lastBalance = parseFloat(lastEntry.balance) || 0;
        const adjustedFirstBalance = firstBalance + totalAddAmount;

        return {
            firstEntry,
            lastEntry,
            firstBalance: adjustedFirstBalance,
            lastBalance,
            totalAddAmount,
            balanceEntries
        };
    },

    // 计算APR
    calculateAPR: (historyData) => {
        if (!historyData) return 0;

        try {
            const firstDate = window.DashboardUtils.parseDate(historyData.firstEntry.date);
            const lastDate = window.DashboardUtils.parseDate(historyData.lastEntry.date);

            if (!firstDate || !lastDate) return 0;

            const timeDiff = lastDate.toMillis() - firstDate.toMillis();
            const daysDiff = Math.max(1, timeDiff / (1000 * 3600 * 24));

            const totalEarned = historyData.lastBalance - historyData.firstBalance;
            const baseAmount = historyData.firstBalance;

            if (baseAmount > 0) {
                const returnRate = totalEarned / baseAmount;
                return Math.round(((returnRate / daysDiff) * 365) * 100 * 100) / 100;
            }
        } catch (e) {
            return 0;
        }

        return 0;
    },

    // 计算投资天数
    calculateInvestDays: (historyData) => {
        if (!historyData) return 0;

        try {
            const firstDate = window.DashboardUtils.parseDate(historyData.firstEntry.date);
            const lastDate = window.DashboardUtils.parseDate(historyData.lastEntry.date);

            if (!firstDate || !lastDate) return 0;

            const timeDiff = lastDate.toMillis() - firstDate.toMillis();
            const daysDiff = Math.max(1, timeDiff / (1000 * 3600 * 24));
            return Math.round(daysDiff * 100) / 100;
        } catch (e) {
            return 0;
        }
    },

    // 计算日期维度的投资天数（使用项目完整历史的第一个日期）
    calculateInvestDaysForDateView: (project, currentDate) => {
        if (!project.file || !project.file.lists || !Array.isArray(project.file.lists)) {
            return 0;
        }

        try {
            // 获取项目的所有balance记录
            const balanceEntries = project.file.lists.filter(item =>
                item &&
                item.balance !== undefined &&
                item.date !== undefined &&
                item.date !== null &&
                item.date.toString().trim() !== ""
            );

            if (balanceEntries.length === 0) return 0;

            // 按日期排序，获取最早的记录
            balanceEntries.sort((a, b) => {
                try {
                    const dateA = window.DashboardUtils.parseDate(a.date);
                    const dateB = window.DashboardUtils.parseDate(b.date);
                    if (!dateA || !dateB) return 0;
                    return dateA.toMillis() - dateB.toMillis();
                } catch (e) {
                    return 0;
                }
            });

            const firstDate = window.DashboardUtils.parseDate(balanceEntries[0].date);
            const endDate = window.DashboardUtils.parseDate(currentDate);

            if (!firstDate || !endDate) return 0;

            const timeDiff = endDate.toMillis() - firstDate.toMillis();
            const daysDiff = Math.max(1, timeDiff / (1000 * 3600 * 24));
            return Math.round(daysDiff * 100) / 100;
        } catch (e) {
            return 0;
        }
    },



    // 计算项目在指定时间范围内的历史记录数据
    calculateProjectHistoryInTimeRange: (project, priceMap, startDate, endDate) => {
        if (!project.file || !project.file.lists || !Array.isArray(project.file.lists)) {
            return null;
        }

        const balanceEntries = project.file.lists.filter(item =>
            item &&
            item.balance !== undefined &&
            item.date !== undefined &&
            item.date !== null &&
            item.date.toString().trim() !== ""
        );

        if (balanceEntries.length === 0) return null;

        // 按日期排序
        balanceEntries.sort((a, b) => {
            try {
                const dateA = window.DashboardUtils.parseDate(a.date);
                const dateB = window.DashboardUtils.parseDate(b.date);
                if (!dateA || !dateB) return 0;
                return dateA.toMillis() - dateB.toMillis();
            } catch (e) {
                return 0;
            }
        });

        // 只处理时间范围内有数据的情况
        const entriesInRange = balanceEntries.filter(entry => {
            try {
                const entryDate = window.DashboardUtils.parseDate(entry.date);
                if (!entryDate) return false;

                const entryDateStr = entryDate.toFormat("yyyy-MM-dd");
                return entryDateStr >= startDate && entryDateStr <= endDate;
            } catch (e) {
                return false;
            }
        });

        // 如果时间范围内没有记录，直接返回null
        if (entriesInRange.length === 0) return null;

        const firstEntry = entriesInRange[0];
        const lastEntry = entriesInRange.length === 1 ? entriesInRange[0] : entriesInRange[entriesInRange.length - 1];

        // 计算时间范围内所有add属性的总和
        let totalAddAmount = 0;
        try {
            for (const entry of entriesInRange) {
                if (entry && entry.add !== undefined && entry.add !== null) {
                    const addValue = parseFloat(entry.add);
                    if (!isNaN(addValue)) {
                        totalAddAmount += addValue;
                    }
                }
            }
        } catch (e) {
            totalAddAmount = 0;
        }

        const firstBalance = parseFloat(firstEntry.balance) || 0;
        const lastBalance = parseFloat(lastEntry.balance) || 0;
        const adjustedFirstBalance = firstBalance + totalAddAmount;

        return {
            firstEntry,
            lastEntry,
            firstBalance: adjustedFirstBalance,
            lastBalance,
            totalAddAmount,
            balanceEntries: entriesInRange
        };
    },

    // 计算指定时间范围内的收益变化（用于日期维度的总收益列）
    calculateTimeRangeEarnings: (project, priceMap, startDate, endDate, unit) => {
        if (!project.file || !project.file.lists || !Array.isArray(project.file.lists)) {
            return { timeRangeEarnings: 0, priceFound: true };
        }

        try {
            // 获取所有balance记录并按日期排序
            const balanceEntries = project.file.lists.filter(item =>
                item &&
                item.balance !== undefined &&
                item.date !== undefined &&
                item.date !== null &&
                item.date.toString().trim() !== ""
            );

            if (balanceEntries.length === 0) {
                return { timeRangeEarnings: 0, priceFound: true };
            }

            balanceEntries.sort((a, b) => {
                try {
                    const dateA = window.DashboardUtils.parseDate(a.date);
                    const dateB = window.DashboardUtils.parseDate(b.date);
                    if (!dateA || !dateB) return 0;
                    return dateA.toMillis() - dateB.toMillis();
                } catch (e) {
                    return 0;
                }
            });

            // 找到时间范围开始前的最近记录作为起始余额
            let startBalance = 0;
            let startBalanceFound = false;

            // 找到时间范围开始前的最后一条记录
            for (let i = balanceEntries.length - 1; i >= 0; i--) {
                const entry = balanceEntries[i];
                const entryDate = window.DashboardUtils.parseDate(entry.date);
                if (entryDate && entryDate.toFormat("yyyy-MM-dd") < startDate) {
                    startBalance = parseFloat(entry.balance) || 0;
                    startBalanceFound = true;
                    break;
                }
            }

            // 找到时间范围内的记录
            const entriesInRange = balanceEntries.filter(entry => {
                try {
                    const entryDate = window.DashboardUtils.parseDate(entry.date);
                    if (!entryDate) return false;
                    const entryDateStr = entryDate.toFormat("yyyy-MM-dd");
                    return entryDateStr >= startDate && entryDateStr <= endDate;
                } catch (e) {
                    return false;
                }
            });

            // 如果时间范围内没有记录，收益为0
            if (entriesInRange.length === 0) {
                return { timeRangeEarnings: 0, priceFound: true };
            }

            // 如果时间范围开始前没有记录，说明项目在这个时间范围内开始
            if (!startBalanceFound) {
                // 使用时间范围内第一条记录的余额作为起始（假设这是初始投资）
                startBalance = parseFloat(entriesInRange[0].balance) || 0;
                // 如果第一条记录有add字段，需要减去，因为这是追加投资
                if (entriesInRange[0].add !== undefined) {
                    startBalance -= parseFloat(entriesInRange[0].add) || 0;
                }
            }

            // 获取时间范围内的最后余额
            const endBalance = parseFloat(entriesInRange[entriesInRange.length - 1].balance) || 0;

            // 计算时间范围内的追加投资
            let addAmountInRange = 0;
            for (const entry of entriesInRange) {
                if (entry.add !== undefined && entry.add !== null) {
                    addAmountInRange += parseFloat(entry.add) || 0;
                }
            }

            // 计算余额变化（不包括追加投资）
            const balanceChange = endBalance - startBalance - addAmountInRange;

            // 转换为USDT价值
            if (unit === 'USDT' || unit === 'USDC' || unit === 'DAI') {
                return { timeRangeEarnings: balanceChange, priceFound: true };
            }

            // 对于其他代币，需要价格转换
            const prices = priceMap.get(unit);
            if (!prices) {
                return { timeRangeEarnings: -1, priceFound: false };
            }

            // 使用时间范围结束日期的价格
            const endDate_parsed = window.DashboardUtils.parseDate(entriesInRange[entriesInRange.length - 1].date);
            const endDateStr = endDate_parsed.toFormat("yyyy-MM-dd");

            if (prices.has(endDateStr)) {
                const price = prices.get(endDateStr);
                return { timeRangeEarnings: balanceChange * price, priceFound: true };
            } else {
                return { timeRangeEarnings: -1, priceFound: false };
            }

        } catch (e) {
            console.error('计算时间范围收益时出错:', e);
            return { timeRangeEarnings: 0, priceFound: false };
        }
    },

    // 计算USDT投入金额（优化版：正确处理追加投资）
    calculateUSDTAmount: (historyData, unit, priceMap) => {
        if (!historyData) return { investAmount: 0, withdrawAmount: 0, priceFound: false };

        try {
            // 对于稳定币，直接返回余额值
            if (unit === 'USDT' || unit === 'USDC' || unit === 'DAI') {
                const firstBalance = parseFloat(historyData.firstEntry.balance) || 0;
                const lastBalance = historyData.lastBalance;
                const totalAddAmount = historyData.totalAddAmount;

                return {
                    investAmount: firstBalance + totalAddAmount,
                    withdrawAmount: lastBalance,
                    priceFound: true,
                    investDate: window.DashboardUtils.parseDate(historyData.firstEntry.date).toFormat("yyyy-MM-dd"),
                    withdrawDate: window.DashboardUtils.parseDate(historyData.lastEntry.date).toFormat("yyyy-MM-dd")
                };
            }

            // 获取价格数据
            const prices = priceMap.get(unit);
            if (!prices) {
                return { investAmount: -1, withdrawAmount: -1, priceFound: false };
            }

            // 计算总投资成本（分别计算每笔投资的USDT价值）
            let totalInvestAmount = 0;
            let allPricesFound = true;

            // 按日期排序的历史记录
            const sortedEntries = [...historyData.balanceEntries].sort((a, b) => {
                const dateA = window.DashboardUtils.parseDate(a.date);
                const dateB = window.DashboardUtils.parseDate(b.date);
                return dateA.toMillis() - dateB.toMillis();
            });

            // 处理初始投资
            const firstEntry = sortedEntries[0];
            const firstDate = window.DashboardUtils.parseDate(firstEntry.date).toFormat("yyyy-MM-dd");
            const firstBalance = parseFloat(firstEntry.balance) || 0;

            if (prices.has(firstDate)) {
                const firstPrice = prices.get(firstDate);
                totalInvestAmount += firstBalance * firstPrice;
            } else {
                allPricesFound = false;
            }

            // 处理追加投资
            for (const entry of sortedEntries) {
                if (entry.add !== undefined && entry.add !== null) {
                    const addAmount = parseFloat(entry.add);
                    if (!isNaN(addAmount) && addAmount > 0) {
                        const addDate = window.DashboardUtils.parseDate(entry.date).toFormat("yyyy-MM-dd");
                        if (prices.has(addDate)) {
                            const addPrice = prices.get(addDate);
                            totalInvestAmount += addAmount * addPrice;
                        } else {
                            allPricesFound = false;
                        }
                    }
                }
            }

            // 计算当前价值（使用最新日期的价格）
            const lastDate = window.DashboardUtils.parseDate(historyData.lastEntry.date).toFormat("yyyy-MM-dd");
            let withdrawAmount = -1;

            if (prices.has(lastDate)) {
                const lastPrice = prices.get(lastDate);
                withdrawAmount = historyData.lastBalance * lastPrice;
            } else {
                allPricesFound = false;
            }

            return {
                investAmount: allPricesFound ? totalInvestAmount : -1,
                withdrawAmount: allPricesFound ? withdrawAmount : -1,
                priceFound: allPricesFound,
                investDate: window.DashboardUtils.parseDate(historyData.firstEntry.date).toFormat("yyyy-MM-dd"),
                withdrawDate: lastDate
            };
        } catch (e) {
            console.error('计算USDT金额时出错:', e);
            return { investAmount: -1, withdrawAmount: -1, priceFound: false };
        }
    }
};

// 模块3加载完成（静默）
```
```dataviewjs
// ===== 衍生指标计算模块 ===== (静默执行)
// 清空当前输出
dv.container.innerHTML = '';

// ===== 衍生指标计算模块 =====
window.DashboardDerivedCalculator = {
    // 基于USDT价值计算APR（优化版）
    calculateUSDTBasedAPR: (usdtAmounts, investDays) => {
        if (!usdtAmounts || usdtAmounts.investAmount === -1 || usdtAmounts.withdrawAmount === -1 || investDays <= 0) {
            return 0;
        }

        try {
            const totalEarned = usdtAmounts.withdrawAmount - usdtAmounts.investAmount;
            const baseAmount = usdtAmounts.investAmount;

            if (baseAmount > 0) {
                const returnRate = totalEarned / baseAmount;
                return Math.round(((returnRate / investDays) * 365) * 100 * 100) / 100;
            }
        } catch (e) {
            console.error('计算USDT基础APR时出错:', e);
            return 0;
        }

        return 0;
    },

    // 计算仓位状态和建议操作（合并仓位等级显示）
    calculatePositionStatus: (investAmount, level, levelInfo) => {
        if (!levelInfo || !levelInfo[level]) {
            return {
                positionStatus: "配置未找到",
                suggestedAction: "检查配置"
            };
        }

        const limitAmount = levelInfo[level].limit || 0;
        const levelText = levelInfo[level].cn || `等级${level}`;

        if (investAmount === -1) {
            return {
                positionStatus: `${levelText} <span style="color: #6c757d;">⚪ 价格未找到</span>`,
                suggestedAction: "价格数据未找到"
            };
        }

        const diff = investAmount - limitAmount;
        const threshold = limitAmount * 0.2; // 20% threshold

        if (investAmount === 0) {
            return {
                positionStatus: `${levelText} <span style="color: #6c757d;">⚪ 未配置</span>`,
                suggestedAction: `加仓 ${window.DashboardUtils.formatNumber(limitAmount)}`
            };
        } else if (Math.abs(diff) <= threshold) {
            return {
                positionStatus: `${levelText} <span style="color: #28a745;">🟢 正常</span>`,
                suggestedAction: "保持"
            };
        } else if (diff > threshold) {
            return {
                positionStatus: `${levelText} <span style="color: #dc3545;">🔴 超配</span>`,
                suggestedAction: `减仓 ${window.DashboardUtils.formatNumber(Math.abs(diff))}`
            };
        } else {
            return {
                positionStatus: `${levelText} <span style="color: #ffc107;">🟡 低配</span>`,
                suggestedAction: `加仓 ${window.DashboardUtils.formatNumber(Math.abs(diff))}`
            };
        }
    },

    // 计算APR Gap
    calculateAPRGap: (currentApr, expectApr, hasInvestment = true) => {
        if (!hasInvestment || expectApr === 0) return 0;
        const expectAprPercent = expectApr * 100;
        return currentApr - expectAprPercent;
    },

    // 计算组合APR
    calculatePortfolioAPR: (currentPercent, apr) => {
        return currentPercent * apr;
    },

    // 计算总收益和日收益
    calculateEarnings: (usdtAmounts, investDays) => {
        if (usdtAmounts.investAmount === -1 || usdtAmounts.withdrawAmount === -1) {
            return {
                totalEarned: -1,
                dailyEarned: -1
            };
        }

        const totalEarned = usdtAmounts.withdrawAmount - usdtAmounts.investAmount;
        const dailyEarned = investDays > 0 ? totalEarned / investDays : 0;

        return {
            totalEarned,
            dailyEarned
        };
    }
};

// 模块4加载完成（静默）
```
```dataviewjs
// ===== 主业务逻辑模块 ===== (静默执行)
// 清空当前输出
dv.container.innerHTML = '';

// ===== 主业务逻辑模块 =====
window.DashboardBusinessLogic = {
    // 处理单个项目数据
    processProject: (project, riskInfo, levelInfo, priceMap) => {
        const riskLevel = project.Risk;
        const riskText = (riskInfo[riskLevel] && riskInfo[riskLevel].text) ? riskInfo[riskLevel].text : `风险 ${riskLevel}`;
        const expectApr = (riskInfo[riskLevel] && riskInfo[riskLevel].expectApr) ? riskInfo[riskLevel].expectApr : 0;
        const unit = (project.Unit || 'USDT').toUpperCase();
        const level = project.Level || 0;

        // 获取仓位等级信息
        const levelText = (levelInfo && levelInfo[level]) ? levelInfo[level].text : `仓位 ${level}`;

        // 计算基础指标
        const historyData = window.DashboardBaseCalculator.calculateProjectHistory(project, priceMap);
        const investDays = window.DashboardBaseCalculator.calculateInvestDays(historyData);
        const usdtAmounts = window.DashboardBaseCalculator.calculateUSDTAmount(historyData, unit, priceMap);

        // 使用优化的APR计算（基于USDT价值）
        const projectApr = window.DashboardDerivedCalculator.calculateUSDTBasedAPR(usdtAmounts, investDays);

        // 计算衍生指标
        const positionInfo = window.DashboardDerivedCalculator.calculatePositionStatus(usdtAmounts.investAmount, level, levelInfo);
        const earnings = window.DashboardDerivedCalculator.calculateEarnings(usdtAmounts, investDays);

        // 设置投资和提取日期
        const investDate = usdtAmounts.investDate || "";
        const withdrawDate = project.Status === "Done" ? (usdtAmounts.withdrawDate || "") : "-";

        return {
            riskLevel,
            riskText,
            expectApr,
            unit,
            level,
            levelText,
            projectApr,
            investDays,
            amountInUsdt: usdtAmounts.priceFound ? usdtAmounts.investAmount : 0,

            // 基础数据
            usdtInvestAmount: usdtAmounts.investAmount,
            usdtWithdrawAmount: usdtAmounts.withdrawAmount,
            usdtTotalEarned: earnings.totalEarned,
            usdtDailyEarned: earnings.dailyEarned,

            // 状态信息
            positionStatus: positionInfo.positionStatus,
            suggestedAction: positionInfo.suggestedAction,

            // 其他信息
            type: project.Type || "-",
            investDate,
            withdrawDate,
            status: project.Status || "Doing",
            projectLink: project.file.link
        };
    },

    // 创建项目详情列表（简化版，不再按协议拆分）
    createProtocolDetails: (projects, riskInfo, levelInfo, priceMap) => {
        const protocolDetails = [];

        for (const project of projects) {
            const projectData = window.DashboardBusinessLogic.processProject(project, riskInfo, levelInfo, priceMap);

            // 直接添加项目数据，不再按协议拆分
            protocolDetails.push({
                protocol: project.Protocol || "-", // 保留协议字段以兼容现有代码
                ...projectData
            });
        }

        return protocolDetails;
    }
};

// 模块5加载完成（静默）
```
```dataviewjs
// ===== 表格数据构建模块 ===== (静默执行)
// 清空当前输出
dv.container.innerHTML = '';

// ===== 表格数据构建模块 =====
window.DashboardTableBuilder = {
    // 计算风险等级汇总数据
    calculateRiskSummary: (protocolDetails, riskInfo) => {
        const amountByRisk = {};
        const aprByRisk = {};
        const expectAprByRisk = {};
        const totalEarnedByRisk = {};
        const maxInvestDaysByRisk = {};
        const dailyEarnedByRisk = {};

        // 初始化所有风险等级
        for (const riskLevel in riskInfo) {
            amountByRisk[riskLevel] = 0;
            aprByRisk[riskLevel] = 0;
            expectAprByRisk[riskLevel] = riskInfo[riskLevel].expectApr || 0;
            totalEarnedByRisk[riskLevel] = 0;
            maxInvestDaysByRisk[riskLevel] = 0;
            dailyEarnedByRisk[riskLevel] = 0;
        }

        // 计算各风险等级的投入总额和收益数据
        for (const detail of protocolDetails) {
            if (amountByRisk[detail.riskLevel] !== undefined) {
                amountByRisk[detail.riskLevel] += detail.amountInUsdt;

                // 累计总收益（只有价格数据可用的项目）
                if (detail.usdtTotalEarned !== -1) {
                    totalEarnedByRisk[detail.riskLevel] += detail.usdtTotalEarned;
                }

                // 记录最大投资天数
                if (detail.investDays > maxInvestDaysByRisk[detail.riskLevel]) {
                    maxInvestDaysByRisk[detail.riskLevel] = detail.investDays;
                }

                // 累计日收益（只有价格数据可用的项目）
                if (detail.usdtDailyEarned !== -1) {
                    dailyEarnedByRisk[detail.riskLevel] += detail.usdtDailyEarned;
                }
            }
        }

        // 计算各风险等级的简单平均APR
        for (const riskLevel in amountByRisk) {
            const projectsInRisk = protocolDetails.filter(p => p.riskLevel == riskLevel);
            if (projectsInRisk.length > 0) {
                const averageApr = projectsInRisk.reduce((sum, p) => sum + p.projectApr, 0) / projectsInRisk.length;
                aprByRisk[riskLevel] = Math.round(averageApr * 100) / 100;
            }
        }

        return {
            amountByRisk,
            aprByRisk,
            expectAprByRisk,
            totalEarnedByRisk,
            maxInvestDaysByRisk,
            dailyEarnedByRisk
        };
    },

    // 构建风险等级汇总行
    buildRiskSummaryRow: (riskLevel, riskInfo, summary, totalAmountInUsdt) => {
        const invested = summary.amountByRisk[riskLevel] || 0;
        const currentPercent = totalAmountInUsdt > 0 ? (invested / totalAmountInUsdt) : 0;
        const limitPercent = riskInfo[riskLevel].percent;
        const currentApr = summary.aprByRisk[riskLevel] || 0;
        const expectApr = summary.expectAprByRisk[riskLevel] || 0;

        // 获取聚合的收益数据
        const totalEarned = summary.totalEarnedByRisk[riskLevel] || 0;
        const maxInvestDays = summary.maxInvestDaysByRisk[riskLevel] || 0;
        const dailyEarned = summary.dailyEarnedByRisk[riskLevel] || 0;

        // 计算各种APR指标
        const expectAprPercent = expectApr * 100;
        const aprGap = (invested > 0 && expectAprPercent > 0) ?
            window.DashboardDerivedCalculator.calculateAPRGap(currentApr, expectApr, true) : 0;
        const portfolioCurrentApr = window.DashboardDerivedCalculator.calculatePortfolioAPR(currentPercent, currentApr);
        const portfolioExpectApr = window.DashboardDerivedCalculator.calculatePortfolioAPR(currentPercent, expectAprPercent);
        const portfolioAprGap = portfolioCurrentApr - portfolioExpectApr;

        // 计算状态文本
        let statusText;
        if (invested === 0) {
            statusText = `⚪ 未配置 (${(limitPercent * 100).toFixed(1)}%)`;
        } else {
            const diff = currentPercent - limitPercent;
            if (diff > 0.05) {
                statusText = `🔴 超配 (${(limitPercent * 100).toFixed(1)}%)`;
            } else if (diff < -0.05) {
                statusText = `🟡 低配 (${(limitPercent * 100).toFixed(1)}%)`;
            } else {
                statusText = `🟢 正常 (${(limitPercent * 100).toFixed(1)}%)`;
            }
        }

        return [
            window.DashboardUtils.getRiskColoredText(riskLevel, riskInfo[riskLevel].text),
            `**${window.DashboardUtils.formatNumber(invested)}**`, // 投入金额（加粗）
            `**${window.DashboardUtils.formatPercentage(currentPercent * 100)}**`, // 当前占比（加粗）
            `**${statusText}**`, // 健康程度（加粗）
            "", // 项目列为空
            // 调整顺序：仓位状态、建议操作、总收益、投资天数、日收益
            "", // 仓位状态（聚合行为空）
            "", // 建议操作（聚合行为空）
            // 总收益（带颜色和粗体）
            totalEarned !== 0 ?
                (totalEarned > 0 ?
                    `<span style="color: #28a745; font-weight: bold;">${window.DashboardUtils.formatNumber(totalEarned)}</span>` :
                    `<span style="color: #dc3545; font-weight: bold;">${window.DashboardUtils.formatNumber(totalEarned)}</span>`) :
                "**0**",
            maxInvestDays > 0 ? `**${maxInvestDays.toFixed(2)}**` : "**0**", // 投资天数（加粗）
            // 日收益（带颜色和粗体）
            dailyEarned !== 0 ?
                (dailyEarned > 0 ?
                    `<span style="color: #28a745; font-weight: bold;">${window.DashboardUtils.formatNumber(dailyEarned)}</span>` :
                    `<span style="color: #dc3545; font-weight: bold;">${window.DashboardUtils.formatNumber(dailyEarned)}</span>`) :
                "**0**",
            // APR相关列
            // 当前APR（带颜色和粗体）
            currentApr > 0 ?
                `<span style="color: #28a745; font-weight: bold;">${window.DashboardUtils.formatPercentage(currentApr)}</span>` :
                currentApr < 0 ?
                    `<span style="color: #dc3545; font-weight: bold;">${window.DashboardUtils.formatPercentage(currentApr)}</span>` :
                    `**${window.DashboardUtils.formatPercentage(currentApr)}**`,
            invested > 0 && expectApr > 0 ? `**${window.DashboardUtils.formatPercentage(expectAprPercent, 0)}**` : "**0%**", // 期望APR（加粗）
            invested > 0 && expectApr > 0 ?
                window.DashboardUtils.getAPRGapColoredText(aprGap).replace('<span style="color:', '<span style="color:').replace(';">', '; font-weight: bold;">') :
                "**0%**", // APR Gap（加粗）
            // 当前APR总比（带颜色和粗体）
            portfolioCurrentApr > 0 ?
                `<span style="color: #28a745; font-weight: bold;">${window.DashboardUtils.formatPercentage(portfolioCurrentApr)}</span>` :
                portfolioCurrentApr < 0 ?
                    `<span style="color: #dc3545; font-weight: bold;">${window.DashboardUtils.formatPercentage(portfolioCurrentApr)}</span>` :
                    `**${window.DashboardUtils.formatPercentage(portfolioCurrentApr)}**`,
            `**${window.DashboardUtils.formatPercentage(portfolioExpectApr)}**`, // 期望APR总比（加粗）
            invested > 0 && expectApr > 0 ?
                window.DashboardUtils.getAPRGapColoredText(portfolioAprGap).replace('<span style="color:', '<span style="color:').replace(';">', '; font-weight: bold;">') :
                "**0%**" // APR Gap总比（加粗）
        ];
    },

    // 构建项目详情行
    buildProjectRow: (project, totalAmountInUsdt) => {
        const projectPercent = totalAmountInUsdt > 0 ? (project.amountInUsdt / totalAmountInUsdt) : 0;
        const projectExpectApr = project.expectApr * 100;
        const projectAprGap = window.DashboardDerivedCalculator.calculateAPRGap(project.projectApr, project.expectApr, project.amountInUsdt > 0);
        const projectPortfolioCurrentApr = window.DashboardDerivedCalculator.calculatePortfolioAPR(projectPercent, project.projectApr);
        const projectPortfolioExpectApr = window.DashboardDerivedCalculator.calculatePortfolioAPR(projectPercent, projectExpectApr);
        const projectPortfolioAprGap = projectPortfolioCurrentApr - projectPortfolioExpectApr;

        return [
            "", // 风险等级列为空
            window.DashboardUtils.formatNumber(project.amountInUsdt),
            window.DashboardUtils.formatPercentage(projectPercent * 100),
            "-", // 健康程度（项目行显示横杠）
            project.projectLink,
            // 调整顺序：仓位状态、建议操作、总收益、投资天数、日收益
            project.positionStatus || "-",
            project.suggestedAction || "-",
            project.usdtTotalEarned === -1 ? "价格数据未找到" :
                (project.usdtTotalEarned !== 0 ? window.DashboardUtils.formatColoredNumber(project.usdtTotalEarned) : "0"), // 总收益（带颜色）
            project.investDays > 0 ? `${project.investDays.toFixed(2)}` : "0",
            project.usdtDailyEarned === -1 ? "价格数据未找到" :
                (project.usdtDailyEarned !== 0 ? window.DashboardUtils.formatColoredNumber(project.usdtDailyEarned) : "0"), // 日收益（带颜色）
            // APR相关列
            window.DashboardUtils.formatColoredPercentage(project.projectApr), // 当前APR（带颜色）
            project.amountInUsdt > 0 && projectExpectApr > 0 ? window.DashboardUtils.formatPercentage(projectExpectApr, 0) : "0%",
            project.amountInUsdt > 0 && projectExpectApr > 0 ? window.DashboardUtils.getAPRGapColoredText(projectAprGap) : "0%",
            window.DashboardUtils.formatColoredPercentage(projectPortfolioCurrentApr), // 当前APR总比（带颜色）
            window.DashboardUtils.formatPercentage(projectPortfolioExpectApr),
            project.amountInUsdt > 0 && projectExpectApr > 0 ? window.DashboardUtils.getAPRGapColoredText(projectPortfolioAprGap) : "0%"
        ];
    }
};

// 模块6加载完成（静默）
```
```dataviewjs
// ===== 日期维度表格构建模块 ===== (静默执行)
// 清空当前输出
dv.container.innerHTML = '';

// ===== 日期维度表格构建模块 =====
window.DashboardDateTableBuilder = {
    // 构建日期维度表格数据
    buildDateTable: (projects, riskInfo, levelInfo, priceMap, granularity = 'monthly') => {
        try {
            // 数据验证
            if (!projects || projects.length === 0) {
                return [];
            }

            if (!riskInfo || !levelInfo || !priceMap) {
                console.warn('日期维度表格构建：缺少必要的配置数据');
                return [];
            }

            // 1. 提取项目日期数据
            const projectDateData = window.DashboardDateAggregator.extractProjectDateData(projects);
            if (projectDateData.length === 0) {
                console.info('日期维度表格构建：未找到有效的日期数据');
                return [];
            }

        // 2. 根据粒度聚合项目日期数据
        let timeGroups = [];
        switch (granularity) {
            case 'daily':
                // 按日聚合：每个日期一组
                const dailyGroups = new Map();
                for (const item of projectDateData) {
                    const dateKey = item.date;
                    if (!dailyGroups.has(dateKey)) {
                        const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
                        const weekday = weekdays[item.dateObj.weekday % 7];
                        dailyGroups.set(dateKey, {
                            timeKey: dateKey,
                            displayText: `${item.dateObj.toFormat("yyyy年M月d日")} (${weekday})`,
                            projectData: []
                        });
                    }
                    dailyGroups.get(dateKey).projectData.push(item);
                }
                timeGroups = Array.from(dailyGroups.values()).sort((a, b) => b.timeKey.localeCompare(a.timeKey));
                break;
            case 'weekly':
                timeGroups = window.DashboardDateAggregator.aggregateByWeek(projectDateData);
                break;
            case 'monthly':
                timeGroups = window.DashboardDateAggregator.aggregateByMonth(projectDateData);
                break;
            case 'yearly':
                timeGroups = window.DashboardDateAggregator.aggregateByYear(projectDateData);
                break;
            default:
                timeGroups = window.DashboardDateAggregator.aggregateByMonth(projectDateData);
        }

        // 3. 构建表格数据
        const tableData = [];

        for (const timeGroup of timeGroups) {
            if (!timeGroup.projectData || timeGroup.projectData.length === 0) continue;

            // 按项目分组处理该时间段的数据
            const projectGroups = new Map();
            for (const item of timeGroup.projectData) {
                const projectKey = item.project.file.path;
                if (!projectGroups.has(projectKey)) {
                    projectGroups.set(projectKey, {
                        project: item.project,
                        entries: []
                    });
                }
                projectGroups.get(projectKey).entries.push(item);
            }

            // 处理每个项目在该时间段的数据
            const protocolDetails = [];

            // 计算该时间段的时间范围
            let timeRange = null;
            if (timeGroup.projectData && timeGroup.projectData.length > 0) {
                const dates = timeGroup.projectData.map(item => item.date).sort();
                timeRange = {
                    startDate: dates[0],
                    endDate: dates[dates.length - 1]
                };

                // 根据粒度调整时间范围
                switch (granularity) {
                    case 'monthly':
                        // 月粒度：使用月份的第一天和最后一天
                        const monthStart = dv.date(dates[0]).startOf('month');
                        const monthEnd = dv.date(dates[0]).endOf('month');
                        timeRange = {
                            startDate: monthStart.toFormat("yyyy-MM-dd"),
                            endDate: monthEnd.toFormat("yyyy-MM-dd")
                        };
                        break;
                    case 'weekly':
                        // 周粒度：使用周的开始和结束
                        if (timeGroup.weekStart && timeGroup.weekEnd) {
                            timeRange = {
                                startDate: timeGroup.weekStart,
                                endDate: timeGroup.weekEnd
                            };
                        }
                        break;
                    case 'yearly':
                        // 年粒度：使用年份的第一天和最后一天
                        const yearStart = dv.date(dates[0]).startOf('year');
                        const yearEnd = dv.date(dates[0]).endOf('year');
                        timeRange = {
                            startDate: yearStart.toFormat("yyyy-MM-dd"),
                            endDate: yearEnd.toFormat("yyyy-MM-dd")
                        };
                        break;
                    case 'daily':
                    default:
                        // 日粒度：保持原有逻辑
                        break;
                }
            }

            for (const [projectKey, projectGroup] of projectGroups) {
                // 使用该时间段内最新的记录作为项目状态
                const latestEntry = projectGroup.entries.sort((a, b) => b.date.localeCompare(a.date))[0];

                const projectData = window.DashboardDateTableBuilder.processProjectDateEntry(
                    projectGroup.project,
                    latestEntry,
                    riskInfo,
                    levelInfo,
                    priceMap,
                    timeRange  // 传递时间范围
                );

                // 只有当projectData不为null时才添加到列表中
                if (projectData) {
                    protocolDetails.push({
                        protocol: projectGroup.project.Protocol || "-", // 保留协议字段以兼容现有代码
                        ...projectData
                    });
                }
            }

            if (protocolDetails.length === 0) continue;

            // 计算该时间段的汇总数据
            const totalAmountInUsdt = protocolDetails.reduce((sum, p) => sum + p.amountInUsdt, 0);
            const avgAPR = protocolDetails.length > 0 ?
                protocolDetails.reduce((sum, p) => sum + p.projectApr, 0) / protocolDetails.length : 0;

            // 添加时间汇总行
            tableData.push(window.DashboardDateTableBuilder.buildTimeSummaryRow(
                timeGroup,
                protocolDetails,
                totalAmountInUsdt,
                avgAPR
            ));

            // 添加项目详情行
            protocolDetails.sort((a, b) => {
                // 按项目链接的文件名排序
                const nameA = a.projectLink && a.projectLink.path ? a.projectLink.path.split('/').pop() : '';
                const nameB = b.projectLink && b.projectLink.path ? b.projectLink.path.split('/').pop() : '';
                return nameA.localeCompare(nameB);
            });
            for (const project of protocolDetails) {
                tableData.push(window.DashboardDateTableBuilder.buildDateProjectRow(
                    project,
                    totalAmountInUsdt
                ));
            }

            // 添加分界线（除了最后一个时间段）
            if (timeGroups.indexOf(timeGroup) < timeGroups.length - 1) {
                tableData.push([
                    "---", "---", "---", "---", "---", "---", "---", "---", "---", "---",
                    "---", "---", "---", "---", "---"
                ]);
            }
        }

        return tableData;

        } catch (error) {
            console.error('日期维度表格构建错误:', error);
            return [];
        }
    },

    // 处理项目日期条目数据（按时间范围计算）
    processProjectDateEntry: (project, dateEntry, riskInfo, levelInfo, priceMap, timeRange) => {
        const riskLevel = project.Risk;
        const riskText = (riskInfo[riskLevel] && riskInfo[riskLevel].text) ? riskInfo[riskLevel].text : `风险 ${riskLevel}`;
        const expectApr = (riskInfo[riskLevel] && riskInfo[riskLevel].expectApr) ? riskInfo[riskLevel].expectApr : 0;
        const unit = (project.Unit || 'USDT').toUpperCase();
        const level = project.Level || 0;

        // 获取完整历史数据用于计算初始投资金额
        const fullHistoryData = window.DashboardBaseCalculator.calculateProjectHistory(project, priceMap);

        // 使用时间范围限制的历史数据来计算当前状态
        let timeRangeHistoryData;
        if (timeRange && timeRange.startDate && timeRange.endDate) {
            // 使用时间范围内的数据计算
            timeRangeHistoryData = window.DashboardBaseCalculator.calculateProjectHistoryInTimeRange(
                project, priceMap, timeRange.startDate, timeRange.endDate
            );
        } else {
            // 回退到完整历史数据
            timeRangeHistoryData = fullHistoryData;
        }

        // 如果时间范围内没有数据，返回空
        if (!timeRangeHistoryData) {
            return null;
        }

        // 计算正确的投资天数：从项目开始到时间范围内的最新日期
        let investDays;
        if (timeRange && timeRange.startDate && timeRange.endDate) {
            // 使用项目完整历史的第一个日期到时间范围内最新日期的天数
            investDays = window.DashboardBaseCalculator.calculateInvestDaysForDateView(
                project, timeRangeHistoryData.lastEntry.date
            );
        } else {
            // 使用完整历史数据计算
            investDays = window.DashboardBaseCalculator.calculateInvestDays(timeRangeHistoryData);
        }

        // 计算USDT金额：使用混合逻辑
        let usdtAmounts;
        if (timeRange && fullHistoryData && timeRangeHistoryData) {
            // 投资金额使用完整历史数据（包括所有追加投资）
            const fullUsdtAmounts = window.DashboardBaseCalculator.calculateUSDTAmount(fullHistoryData, unit, priceMap);
            // 当前价值使用时间范围内的最新数据
            const currentBalance = timeRangeHistoryData.lastBalance;

            // 计算当前价值的USDT金额
            let currentUsdtValue;
            if (unit === 'USDT' || unit === 'USDC' || unit === 'DAI') {
                currentUsdtValue = currentBalance;
            } else {
                const prices = priceMap.get(unit);
                const currentDate = window.DashboardUtils.parseDate(timeRangeHistoryData.lastEntry.date).toFormat("yyyy-MM-dd");
                if (prices && prices.has(currentDate)) {
                    currentUsdtValue = currentBalance * prices.get(currentDate);
                } else {
                    currentUsdtValue = -1; // 价格未找到
                }
            }

            usdtAmounts = {
                investAmount: fullUsdtAmounts.investAmount,
                withdrawAmount: currentUsdtValue,
                priceFound: fullUsdtAmounts.priceFound && currentUsdtValue !== -1,
                investDate: fullUsdtAmounts.investDate,
                withdrawDate: window.DashboardUtils.parseDate(timeRangeHistoryData.lastEntry.date).toFormat("yyyy-MM-dd")
            };
        } else {
            // 回退到原有逻辑
            usdtAmounts = window.DashboardBaseCalculator.calculateUSDTAmount(timeRangeHistoryData, unit, priceMap);
        }

        // 使用优化的APR计算（基于USDT价值）
        const projectApr = window.DashboardDerivedCalculator.calculateUSDTBasedAPR(usdtAmounts, investDays);

        // 计算时间范围内的收益变化（用于总收益列）
        let timeRangeEarnings = { timeRangeEarnings: 0, priceFound: true };
        if (timeRange && timeRange.startDate && timeRange.endDate) {
            timeRangeEarnings = window.DashboardBaseCalculator.calculateTimeRangeEarnings(
                project, priceMap, timeRange.startDate, timeRange.endDate, unit
            );
        }

        // 计算衍生指标
        const positionInfo = window.DashboardDerivedCalculator.calculatePositionStatus(usdtAmounts.investAmount, level, levelInfo);
        const earnings = window.DashboardDerivedCalculator.calculateEarnings(usdtAmounts, investDays);

        return {
            riskLevel,
            riskText,
            expectApr,
            unit,
            level,
            projectApr,
            investDays,
            amountInUsdt: usdtAmounts.priceFound ? usdtAmounts.investAmount : 0,
            usdtInvestAmount: usdtAmounts.investAmount,
            usdtWithdrawAmount: usdtAmounts.withdrawAmount,
            // 使用时间范围收益作为总收益（当前时间维度的收益变化）
            usdtTotalEarned: timeRangeEarnings.priceFound ? timeRangeEarnings.timeRangeEarnings : -1,
            usdtDailyEarned: earnings.dailyEarned, // 保持基于累计数据的日收益计算
            positionStatus: positionInfo.positionStatus,
            suggestedAction: positionInfo.suggestedAction,
            type: project.Type || "-",
            status: project.Status || "Doing",
            projectLink: project.file.link,
            entryDate: dateEntry.date,
            entryBalance: parseFloat(dateEntry.entry.balance) || 0
        };
    },

    // 构建时间汇总行
    buildTimeSummaryRow: (timeGroup, protocolDetails, totalAmountInUsdt, avgAPR) => {
        // 计算聚合数据（基于时间范围限制的数据）
        const totalEarned = protocolDetails.reduce((sum, p) => {
            return sum + (p.usdtTotalEarned !== -1 ? p.usdtTotalEarned : 0);
        }, 0);

        const maxInvestDays = Math.max(...protocolDetails.map(p => p.investDays || 0), 0);

        const totalDailyEarned = protocolDetails.reduce((sum, p) => {
            return sum + (p.usdtDailyEarned !== -1 ? p.usdtDailyEarned : 0);
        }, 0);

        // 计算期望APR平均值
        const validExpectAprs = protocolDetails.filter(p => p.expectApr > 0).map(p => p.expectApr * 100);
        const avgExpectApr = validExpectAprs.length > 0 ?
            validExpectAprs.reduce((sum, apr) => sum + apr, 0) / validExpectAprs.length : 0;

        // 计算APR Gap平均值
        const validAprGaps = protocolDetails.filter(p => p.amountInUsdt > 0 && p.expectApr > 0)
            .map(p => window.DashboardDerivedCalculator.calculateAPRGap(p.projectApr, p.expectApr, true));
        const avgAprGap = validAprGaps.length > 0 ?
            validAprGaps.reduce((sum, gap) => sum + gap, 0) / validAprGaps.length : 0;

        // 计算期望APR总比平均值
        const validExpectAprTotals = protocolDetails.filter(p => p.amountInUsdt > 0 && p.expectApr > 0)
            .map(p => {
                const projectPercent = totalAmountInUsdt > 0 ? (p.amountInUsdt / totalAmountInUsdt) : 0;
                return window.DashboardDerivedCalculator.calculatePortfolioAPR(projectPercent, p.expectApr * 100);
            });
        const avgExpectAprTotal = validExpectAprTotals.length > 0 ?
            validExpectAprTotals.reduce((sum, apr) => sum + apr, 0) / validExpectAprTotals.length : 0;

        // 计算当前APR总比平均值
        const validCurrentAprTotals = protocolDetails.filter(p => p.amountInUsdt > 0)
            .map(p => {
                const projectPercent = totalAmountInUsdt > 0 ? (p.amountInUsdt / totalAmountInUsdt) : 0;
                return window.DashboardDerivedCalculator.calculatePortfolioAPR(projectPercent, p.projectApr);
            });
        const avgCurrentAprTotal = validCurrentAprTotals.length > 0 ?
            validCurrentAprTotals.reduce((sum, apr) => sum + apr, 0) / validCurrentAprTotals.length : 0;

        // 计算APR Gap总比平均值
        const avgAprGapTotal = avgCurrentAprTotal - avgExpectAprTotal;

        return [
            `**${timeGroup.displayText}**`,
            window.DashboardUtils.formatNumber(totalAmountInUsdt),
            totalAmountInUsdt > 0 ? window.DashboardUtils.formatPercentage((totalAmountInUsdt / totalAmountInUsdt) * 100) : "0%",
            `📊 ${protocolDetails.length}个项目`, // 健康程度显示项目数量
            "", // 项目列为空
            // 状态列为空（汇总行）
            "",
            // 聚合数据列
            totalEarned !== 0 ? window.DashboardUtils.formatNumber(totalEarned) : "0", // 总收益
            maxInvestDays > 0 ? `${maxInvestDays.toFixed(2)}` : "0", // 投资天数（最大值）
            totalDailyEarned !== 0 ? window.DashboardUtils.formatNumber(totalDailyEarned) : "0", // 日收益
            // APR相关列
            window.DashboardUtils.formatPercentage(avgAPR),
            avgExpectApr > 0 ? window.DashboardUtils.formatPercentage(avgExpectApr, 0) : "0%", // 期望APR平均值
            validAprGaps.length > 0 ? window.DashboardUtils.getAPRGapColoredText(avgAprGap) : "0%", // APR Gap平均值
            window.DashboardUtils.formatPercentage(avgCurrentAprTotal), // 当前APR总比平均值
            avgExpectAprTotal > 0 ? window.DashboardUtils.formatPercentage(avgExpectAprTotal) : "0%", // 期望APR总比平均值
            validAprGaps.length > 0 ? window.DashboardUtils.getAPRGapColoredText(avgAprGapTotal) : "0%" // APR Gap总比平均值
        ];
    },

    // 构建日期项目详情行
    buildDateProjectRow: (project, totalAmountInUsdt) => {
        const projectPercent = totalAmountInUsdt > 0 ? (project.amountInUsdt / totalAmountInUsdt) : 0;
        const projectExpectApr = project.expectApr * 100;
        const projectAprGap = window.DashboardDerivedCalculator.calculateAPRGap(project.projectApr, project.expectApr, project.amountInUsdt > 0);
        const projectPortfolioCurrentApr = window.DashboardDerivedCalculator.calculatePortfolioAPR(projectPercent, project.projectApr);
        const projectPortfolioExpectApr = window.DashboardDerivedCalculator.calculatePortfolioAPR(projectPercent, projectExpectApr);
        const projectPortfolioAprGap = projectPortfolioCurrentApr - projectPortfolioExpectApr;

        return [
            "", // 时间列为空
            window.DashboardUtils.formatNumber(project.amountInUsdt),
            window.DashboardUtils.formatPercentage(projectPercent * 100),
            "-", // 健康程度（项目行显示横杠）
            project.projectLink,
            // 状态列：使用带颜色标记的状态显示
            window.DashboardUtils.formatProjectStatus(project.status),
            // 总收益、投资天数、日收益
            project.usdtTotalEarned === -1 ? "价格数据未找到" :
                (project.usdtTotalEarned !== 0 ? window.DashboardUtils.formatNumber(project.usdtTotalEarned) : "0"),
            project.investDays > 0 ? `${project.investDays.toFixed(2)}` : "0",
            project.usdtDailyEarned === -1 ? "价格数据未找到" :
                (project.usdtDailyEarned !== 0 ? window.DashboardUtils.formatNumber(project.usdtDailyEarned) : "0"),
            // APR相关列
            window.DashboardUtils.formatPercentage(project.projectApr),
            project.amountInUsdt > 0 && projectExpectApr > 0 ? window.DashboardUtils.formatPercentage(projectExpectApr, 0) : "0%",
            project.amountInUsdt > 0 && projectExpectApr > 0 ? window.DashboardUtils.getAPRGapColoredText(projectAprGap) : "0%",
            window.DashboardUtils.formatPercentage(projectPortfolioCurrentApr),
            window.DashboardUtils.formatPercentage(projectPortfolioExpectApr),
            project.amountInUsdt > 0 && projectExpectApr > 0 ? window.DashboardUtils.getAPRGapColoredText(projectPortfolioAprGap) : "0%"
        ];
    }
};

// 模块6A日期表格构建完成（静默）
```
```dataviewjs
// ===== 主执行逻辑 ===== (静默执行)
// 清空当前输出
dv.container.innerHTML = '';

// ===== 主执行逻辑 =====
try {
    // 1. 加载所有基础数据
    const { riskInfo, levelInfo, priceMap, doingProjects, allProjects } = window.DashboardDataLoader.loadAllData();

    // 2. 数据验证
    if (!riskInfo) {
        dv.error("错误: 风险百分比限制文件 `risk_percentage.md` 未找到或格式不正确。");
    } else if (!levelInfo) {
        dv.error("错误: 投资等级限制文件 `invest_level_limit.md` 未找到或格式不正确。");
    } else if (doingProjects.length === 0) {
        dv.paragraph("✅ 当前没有正在进行中的项目。");
    } else {
        // 3. 处理项目数据，生成协议详情列表
        const protocolDetails = window.DashboardBusinessLogic.createProtocolDetails(doingProjects, riskInfo, levelInfo, priceMap);

        // 4. 计算汇总数据
        const totalAmountInUsdt = protocolDetails.reduce((sum, p) => sum + p.amountInUsdt, 0);
        const riskSummary = window.DashboardTableBuilder.calculateRiskSummary(protocolDetails, riskInfo);

        // 5. 构建表格数据
        const combinedTableData = [];
        const sortedRiskLevels = Object.keys(riskInfo).sort((a, b) => parseInt(a) - parseInt(b));

        for (const riskLevel of sortedRiskLevels) {
            // 添加风险等级汇总行
            combinedTableData.push(
                window.DashboardTableBuilder.buildRiskSummaryRow(riskLevel, riskInfo, riskSummary, totalAmountInUsdt)
            );

            // 添加该风险等级下的项目详情行
            const projectsInThisRisk = protocolDetails.filter(p => p.riskLevel == riskLevel);
            projectsInThisRisk.sort((a, b) => {
                // 按项目链接的文件名排序
                const nameA = a.projectLink && a.projectLink.path ? a.projectLink.path.split('/').pop() : '';
                const nameB = b.projectLink && b.projectLink.path ? b.projectLink.path.split('/').pop() : '';
                return nameA.localeCompare(nameB);
            });

            for (const project of projectsInThisRisk) {
                combinedTableData.push(window.DashboardTableBuilder.buildProjectRow(project, totalAmountInUsdt));
            }

            // 添加风险等级分界线（除了最后一个风险等级）
            const currentIndex = sortedRiskLevels.indexOf(riskLevel);
            if (currentIndex < sortedRiskLevels.length - 1) {
                combinedTableData.push([
                    "---", "---", "---", "---", "---", "---", "---", "---", "---", "---", "---",
                    "---", "---", "---", "---", "---"
                ]);
            }
        }

        // 6. 计算总体数据并添加总计行
        const riskLevelsWithProjects = Object.keys(riskSummary.aprByRisk).filter(riskLevel => {
            const projectsInRisk = protocolDetails.filter(p => p.riskLevel == riskLevel);
            return projectsInRisk.length > 0;
        });

        const totalCurrentApr = riskLevelsWithProjects.length > 0 ?
            riskLevelsWithProjects.reduce((sum, riskLevel) => sum + (riskSummary.aprByRisk[riskLevel] || 0), 0) / riskLevelsWithProjects.length : 0;

        const totalExpectApr = riskLevelsWithProjects.length > 0 ?
            (riskLevelsWithProjects.reduce((sum, riskLevel) => sum + (riskSummary.expectAprByRisk[riskLevel] || 0), 0) / riskLevelsWithProjects.length) * 100 : 0;

        // 计算总体收益数据
        const grandTotalEarned = riskLevelsWithProjects.reduce((sum, riskLevel) => sum + (riskSummary.totalEarnedByRisk[riskLevel] || 0), 0);
        const grandMaxInvestDays = Math.max(...riskLevelsWithProjects.map(riskLevel => riskSummary.maxInvestDaysByRisk[riskLevel] || 0), 0);
        const grandDailyEarned = riskLevelsWithProjects.reduce((sum, riskLevel) => sum + (riskSummary.dailyEarnedByRisk[riskLevel] || 0), 0);

        const totalAprGap = window.DashboardDerivedCalculator.calculateAPRGap(totalCurrentApr, totalExpectApr / 100, totalAmountInUsdt > 0);
        const totalPortfolioAprGap = totalCurrentApr - totalExpectApr;

        // 添加总计行
        combinedTableData.push([
            "**总计**",
            `**${window.DashboardUtils.formatNumber(totalAmountInUsdt)}**`,
            totalAmountInUsdt > 0 ? "**100.00%**" : "**0.00%**",
            "",
            "",
            // 调整顺序：仓位状态、建议操作、总收益、投资天数、日收益
            "", // 仓位状态（总计行为空）
            "", // 建议操作（总计行为空）
            // 总收益（带颜色和粗体）
            grandTotalEarned !== 0 ?
                (grandTotalEarned > 0 ?
                    `<span style="color: #28a745; font-weight: bold;">${window.DashboardUtils.formatNumber(grandTotalEarned)}</span>` :
                    `<span style="color: #dc3545; font-weight: bold;">${window.DashboardUtils.formatNumber(grandTotalEarned)}</span>`) :
                "**0**",
            grandMaxInvestDays > 0 ? `**${grandMaxInvestDays.toFixed(2)}**` : "**0**", // 投资天数（取最大值）
            // 日收益（带颜色和粗体）
            grandDailyEarned !== 0 ?
                (grandDailyEarned > 0 ?
                    `<span style="color: #28a745; font-weight: bold;">${window.DashboardUtils.formatNumber(grandDailyEarned)}</span>` :
                    `<span style="color: #dc3545; font-weight: bold;">${window.DashboardUtils.formatNumber(grandDailyEarned)}</span>`) :
                "**0**",
            // APR相关列
            // 当前APR（带颜色和粗体）
            totalAmountInUsdt > 0 ?
                (totalCurrentApr > 0 ?
                    `<span style="color: #28a745; font-weight: bold;">${window.DashboardUtils.formatPercentage(totalCurrentApr)}</span>` :
                    totalCurrentApr < 0 ?
                        `<span style="color: #dc3545; font-weight: bold;">${window.DashboardUtils.formatPercentage(totalCurrentApr)}</span>` :
                        `**${window.DashboardUtils.formatPercentage(totalCurrentApr)}**`) :
                "**0%**",
            totalExpectApr > 0 ? `**${window.DashboardUtils.formatPercentage(totalExpectApr)}**` : "**0%**",
            totalAprGap !== 0 ? window.DashboardUtils.getAPRGapColoredText(totalAprGap).replace('<span style="color:', '<span style="color:').replace(';">', '; font-weight: bold;">') : `<span style="font-weight: bold;">0%</span>`,
            // 当前APR总比（带颜色和粗体）
            totalAmountInUsdt > 0 ?
                (totalCurrentApr > 0 ?
                    `<span style="color: #28a745; font-weight: bold;">${window.DashboardUtils.formatPercentage(totalCurrentApr)}</span>` :
                    totalCurrentApr < 0 ?
                        `<span style="color: #dc3545; font-weight: bold;">${window.DashboardUtils.formatPercentage(totalCurrentApr)}</span>` :
                        `**${window.DashboardUtils.formatPercentage(totalCurrentApr)}**`) :
                "**0%**",
            totalExpectApr > 0 ? `**${window.DashboardUtils.formatPercentage(totalExpectApr)}**` : "**0%**",
            totalPortfolioAprGap !== 0 ? window.DashboardUtils.getAPRGapColoredText(totalPortfolioAprGap).replace('<span style="color:', '<span style="color:').replace(';">', '; font-weight: bold;">') : `<span style="font-weight: bold;">0%</span>`
        ]);

        // 存储风险等级表格数据到全局变量
        window.DashboardTableData = combinedTableData;
        console.log('风险等级表格数据已准备完成，项目数量:', doingProjects.length);

        // 存储基础数据供日期维度使用（按需计算）
        window.DashboardDateBaseData = {
            projects: allProjects, // 日期视图使用所有项目数据
            riskInfo: riskInfo,
            levelInfo: levelInfo,
            priceMap: priceMap
        };

        // 初始化日期维度表格数据缓存
        window.DashboardDateTableData = {};
        console.log('日期维度基础数据已准备完成，所有项目数量:', allProjects.length);

        // 模块7执行完成（静默）
    }
} catch (error) {
    dv.error(`执行错误: ${error.message}`);
}
```
```dataviewjs
// ===== 维度切换器UI组件 ===== (静默执行)
// 清空当前输出
dv.container.innerHTML = '';

// ===== 维度切换器模块 =====
window.DashboardViewSwitcher = {
    // 渲染维度切换器UI
    renderViewSwitcher: () => {
        const switcherHTML = `
            <div id="dashboard-view-switcher" style="margin-bottom: 20px; text-align: center; padding: 15px; background-color: var(--background-secondary); border-radius: 8px; border: 1px solid var(--background-modifier-border);">
                <!-- 主维度切换 -->
                <div style="margin-bottom: 12px;">
                    <button onclick="window.DashboardViewSwitcher.switchMainView('risk')" id="risk-view-btn" class="main-view-btn" style="margin-right: 10px; padding: 8px 16px; border: 1px solid var(--background-modifier-border); border-radius: 6px; background-color: var(--background-primary); color: var(--text-normal); cursor: pointer; font-weight: 600;">风险等级视图</button>
                    <button onclick="window.DashboardViewSwitcher.switchMainView('date')" id="date-view-btn" class="main-view-btn" style="padding: 8px 16px; border: 1px solid var(--background-modifier-border); border-radius: 6px; background-color: var(--background-primary); color: var(--text-normal); cursor: pointer; font-weight: 600;">日期视图</button>
                </div>

                <!-- 日期子维度切换（仅在日期视图时显示） -->
                <div id="date-granularity-switcher" style="display: none;">
                    <span style="color: var(--text-muted); font-size: 12px; margin-right: 10px;">时间粒度：</span>
                    <button onclick="window.DashboardViewSwitcher.switchDateView('daily')" id="daily-btn" class="date-view-btn" style="margin-right: 5px; padding: 6px 12px; border: 1px solid var(--background-modifier-border); border-radius: 4px; background-color: var(--background-primary); color: var(--text-normal); cursor: pointer; font-size: 12px;">按日</button>
                    <button onclick="window.DashboardViewSwitcher.switchDateView('weekly')" id="weekly-btn" class="date-view-btn" style="margin-right: 5px; padding: 6px 12px; border: 1px solid var(--background-modifier-border); border-radius: 4px; background-color: var(--background-primary); color: var(--text-normal); cursor: pointer; font-size: 12px;">按周</button>
                    <button onclick="window.DashboardViewSwitcher.switchDateView('monthly')" id="monthly-btn" class="date-view-btn" style="margin-right: 5px; padding: 6px 12px; border: 1px solid var(--background-modifier-border); border-radius: 4px; background-color: var(--background-primary); color: var(--text-normal); cursor: pointer; font-size: 12px;">按月</button>
                    <button onclick="window.DashboardViewSwitcher.switchDateView('yearly')" id="yearly-btn" class="date-view-btn" style="padding: 6px 12px; border: 1px solid var(--background-modifier-border); border-radius: 4px; background-color: var(--background-primary); color: var(--text-normal); cursor: pointer; font-size: 12px;">按年</button>
                </div>
            </div>
        `;
        return switcherHTML;
    },

    // 切换主维度视图
    switchMainView: (view) => {
        window.DashboardViewState.mainView = view;

        // 更新按钮样式
        const riskBtn = document.getElementById('risk-view-btn');
        const dateBtn = document.getElementById('date-view-btn');
        const dateGranularitySwitcher = document.getElementById('date-granularity-switcher');

        if (view === 'risk') {
            riskBtn.style.backgroundColor = 'var(--interactive-accent)';
            riskBtn.style.color = 'var(--text-on-accent)';
            dateBtn.style.backgroundColor = 'var(--background-primary)';
            dateBtn.style.color = 'var(--text-normal)';
            dateGranularitySwitcher.style.display = 'none';
        } else {
            dateBtn.style.backgroundColor = 'var(--interactive-accent)';
            dateBtn.style.color = 'var(--text-on-accent)';
            riskBtn.style.backgroundColor = 'var(--background-primary)';
            riskBtn.style.color = 'var(--text-normal)';
            dateGranularitySwitcher.style.display = 'block';

            // 更新日期粒度按钮样式
            window.DashboardViewSwitcher.updateDateGranularityButtons();
        }

        // 重新渲染表格
        window.DashboardViewSwitcher.renderCurrentView();
    },

    // 切换日期粒度视图
    switchDateView: (granularity) => {
        window.DashboardViewState.dateGranularity = granularity;
        window.DashboardViewSwitcher.updateDateGranularityButtons();

        // 重新渲染表格
        window.DashboardViewSwitcher.renderCurrentView();
    },

    // 更新日期粒度按钮样式
    updateDateGranularityButtons: () => {
        const buttons = ['daily-btn', 'weekly-btn', 'monthly-btn', 'yearly-btn'];
        const granularities = ['daily', 'weekly', 'monthly', 'yearly'];

        buttons.forEach((btnId, index) => {
            const btn = document.getElementById(btnId);
            if (btn) {
                if (granularities[index] === window.DashboardViewState.dateGranularity) {
                    btn.style.backgroundColor = 'var(--interactive-accent)';
                    btn.style.color = 'var(--text-on-accent)';
                } else {
                    btn.style.backgroundColor = 'var(--background-primary)';
                    btn.style.color = 'var(--text-normal)';
                }
            }
        });
    },

    // 渲染当前视图
    renderCurrentView: () => {
        console.log('开始渲染视图:', window.DashboardViewState.mainView);

        // 等待数据加载完成
        setTimeout(() => {
            console.log('检查数据可用性...');
            if (window.DashboardViewState.mainView === 'risk') {
                // 显示风险等级表格
                console.log('风险等级数据可用:', !!window.DashboardTableData, '渲染器可用:', !!window.DashboardRenderer);
                if (window.DashboardTableData && window.DashboardRenderer) {
                    window.DashboardRenderer.renderRiskTable();
                } else {
                    console.error('风险等级数据或渲染器不可用');
                }
            } else {
                // 显示日期表格
                console.log('日期基础数据可用:', !!window.DashboardDateBaseData, '渲染器可用:', !!window.DashboardRenderer);
                if (window.DashboardDateBaseData && window.DashboardRenderer) {
                    window.DashboardRenderer.renderDateTable();
                } else {
                    console.error('日期基础数据或渲染器不可用');
                    // 显示错误提示
                    dv.paragraph("⚠️ 日期维度数据正在计算中，请稍候...");
                }
            }
        }, 100);
    }
};

// 模块8A维度切换器加载完成（静默）
```

```dataviewjs
// ===== 表格渲染器 =====
try {
    // 先渲染维度切换器
    const switcherHTML = window.DashboardViewSwitcher.renderViewSwitcher();
    dv.container.innerHTML = switcherHTML;

    // 初始化按钮样式
    setTimeout(() => {
        window.DashboardViewSwitcher.switchMainView('risk');
    }, 100);

    if (window.DashboardTableData) {
        // 渲染默认的风险等级表格（使用固定表头和第一列）
        const tableHTML = window.DashboardTableRenderer.renderFixedTable(
            ["风险等级", "投入金额 (USD)", "当前占比", "健康程度", "项目", "仓位状态", "建议操作", "总收益 (USD)", "投资天数", "日收益 (USD)", "当前APR", "期望APR", "APR Gap", "当前APR总比", "期望APR总比", "APR Gap"],
            window.DashboardTableData,
            'risk-table'
        );

        // 将表格HTML添加到容器中
        const tableContainer = document.createElement('div');
        tableContainer.innerHTML = tableHTML;
        dv.container.appendChild(tableContainer);

        // 存储渲染函数供切换使用
        window.DashboardRenderer = {
            renderRiskTable: () => {
                console.log('开始渲染风险等级表格...');

                if (!window.DashboardTableData) {
                    console.error('风险等级表格数据不可用');
                    dv.container.innerHTML = `
                        <div style="text-align: center; padding: 20px; color: var(--text-muted);">
                            <p>❌ 风险等级表格数据不可用</p>
                            <p style="font-size: 12px;">请确保项目数据已正确加载</p>
                        </div>
                    `;
                    return;
                }

                console.log('风险等级表格数据可用，行数:', window.DashboardTableData.length);

                try {
                    // 清空容器并重新渲染
                    dv.container.innerHTML = '';

                    // 重新添加切换器
                    const switcherHTML = window.DashboardViewSwitcher.renderViewSwitcher();
                    dv.container.innerHTML = switcherHTML;

                    // 更新按钮样式（不触发重新渲染）
                    setTimeout(() => {
                        const riskBtn = document.getElementById('risk-view-btn');
                        const dateBtn = document.getElementById('date-view-btn');
                        const dateGranularitySwitcher = document.getElementById('date-granularity-switcher');

                        if (riskBtn && dateBtn && dateGranularitySwitcher) {
                            riskBtn.style.backgroundColor = 'var(--interactive-accent)';
                            riskBtn.style.color = 'var(--text-on-accent)';
                            dateBtn.style.backgroundColor = 'var(--background-primary)';
                            dateBtn.style.color = 'var(--text-normal)';
                            dateGranularitySwitcher.style.display = 'none';
                        }
                    }, 50);

                    // 渲染风险等级表格（使用固定表头和第一列）
                    const tableHTML = window.DashboardTableRenderer.renderFixedTable(
                        ["风险等级", "投入金额 (USD)", "当前占比", "健康程度", "项目", "仓位状态", "建议操作", "总收益 (USD)", "投资天数", "日收益 (USD)", "当前APR", "期望APR", "APR Gap", "当前APR总比", "期望APR总比", "APR Gap"],
                        window.DashboardTableData,
                        'risk-table'
                    );

                    // 将表格HTML添加到容器中
                    const tableContainer = document.createElement('div');
                    tableContainer.innerHTML = tableHTML;
                    dv.container.appendChild(tableContainer);
                    console.log('风险等级表格渲染成功');
                } catch (error) {
                    console.error('表格渲染错误:', error);
                    dv.container.innerHTML = `
                        <div style="text-align: center; padding: 20px; color: red;">
                            <p>❌ 表格渲染失败: ${error.message}</p>
                        </div>
                    `;
                }
            },

            renderDateTable: () => {
                console.log('开始渲染日期表格...');

                if (!window.DashboardDateBaseData) {
                    console.error('日期基础数据不可用');
                    // 清空容器并显示错误信息
                    dv.container.innerHTML = `
                        <div style="text-align: center; padding: 20px; color: var(--text-muted);">
                            <p>❌ 日期基础数据不可用</p>
                            <p style="font-size: 12px;">请确保项目数据已正确加载</p>
                        </div>
                    `;
                    return;
                }

                const granularity = window.DashboardViewState.dateGranularity;
                console.log('当前粒度:', granularity);

                try {
                    // 按需计算日期表格数据
                    if (!window.DashboardDateTableData[granularity]) {
                        console.log(`正在计算${granularity}粒度的日期数据...`);
                        window.DashboardDateTableData[granularity] = window.DashboardDateTableBuilder.buildDateTable(
                            window.DashboardDateBaseData.projects,
                            window.DashboardDateBaseData.riskInfo,
                            window.DashboardDateBaseData.levelInfo,
                            window.DashboardDateBaseData.priceMap,
                            granularity
                        );
                    }

                    const dateTableData = window.DashboardDateTableData[granularity];
                    console.log('日期表格数据:', dateTableData ? dateTableData.length : 0, '行');

                    if (!dateTableData || dateTableData.length === 0) {
                        const granularityText = {
                            'daily': '按日',
                            'weekly': '按周',
                            'monthly': '按月',
                            'yearly': '按年'
                        };

                        // 清空容器并显示无数据信息
                        dv.container.innerHTML = `
                            <div style="text-align: center; padding: 40px 20px; color: var(--text-muted);">
                                <h3 style="margin-bottom: 10px;">📊 ${granularityText[granularity]}视图</h3>
                                <p style="margin-bottom: 15px;">暂无${granularityText[granularity]}数据</p>
                                <p style="font-size: 12px; color: var(--text-faint);">
                                    可能原因：<br>
                                    • 当前时间段内没有项目活动<br>
                                    • 项目历史记录中缺少日期信息<br>
                                    • 请尝试切换到其他时间粒度查看
                                </p>
                            </div>
                        `;
                        return;
                    }

                    // 根据粒度调整第一列标题
                    const firstColumnTitle = granularity === 'daily' ? '日期' :
                                           granularity === 'weekly' ? '周' :
                                           granularity === 'monthly' ? '月份' : '年份';

                    // 清空容器并重新渲染表格
                    dv.container.innerHTML = '';

                    // 重新添加切换器
                    const switcherHTML = window.DashboardViewSwitcher.renderViewSwitcher();
                    dv.container.innerHTML = switcherHTML;

                    // 更新按钮样式（不触发重新渲染）
                    setTimeout(() => {
                        const riskBtn = document.getElementById('risk-view-btn');
                        const dateBtn = document.getElementById('date-view-btn');
                        const dateGranularitySwitcher = document.getElementById('date-granularity-switcher');

                        if (riskBtn && dateBtn && dateGranularitySwitcher) {
                            dateBtn.style.backgroundColor = 'var(--interactive-accent)';
                            dateBtn.style.color = 'var(--text-on-accent)';
                            riskBtn.style.backgroundColor = 'var(--background-primary)';
                            riskBtn.style.color = 'var(--text-normal)';
                            dateGranularitySwitcher.style.display = 'block';

                            // 更新日期粒度按钮样式
                            window.DashboardViewSwitcher.updateDateGranularityButtons();
                        }
                    }, 50);

                    // 渲染日期表格（使用固定表头和第一列）
                    const tableHTML = window.DashboardTableRenderer.renderFixedTable(
                        [firstColumnTitle, "投入金额 (USD)", "当前占比", "健康程度", "项目", "状态", "总收益 (USD)", "投资天数", "日收益 (USD)", "当前APR", "期望APR", "APR Gap", "当前APR总比", "期望APR总比", "APR Gap总比"],
                        dateTableData,
                        'date-table'
                    );

                    // 将表格HTML添加到容器中
                    const tableContainer = document.createElement('div');
                    tableContainer.innerHTML = tableHTML;
                    dv.container.appendChild(tableContainer);

                    console.log('日期表格渲染成功');

                } catch (error) {
                    console.error('日期表格渲染错误:', error);
                    dv.container.innerHTML = `
                        <div style="text-align: center; padding: 20px; color: red;">
                            <p>❌ 日期表格渲染失败: ${error.message}</p>
                        </div>
                    `;
                }
            }
        };

        // 模块8表格渲染完成（静默）
    } else {
        // 等待数据加载（静默）
    }
} catch (error) {
    dv.error(`表格渲染错误: ${error.message}`);
}
```
