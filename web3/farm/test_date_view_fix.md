# 日期视图修复测试

## 问题描述

在日期视图的日粒度中发现以下问题：
1. 总收益都显示为 0
2. 投资天数都显示为 1 天

## 问题根源分析

### 原始问题
- `calculateProjectHistoryInTimeRange` 函数严格按时间范围过滤数据
- 对于日粒度，时间范围就是单天（startDate = endDate）
- 如果某天只有一条记录，firstEntry 和 lastEntry 是同一条记录
- 导致：总收益 = lastBalance - firstBalance = 0，投资天数 = 1

### 修复方案
1. **新增函数**：`calculateInvestDaysForDateView` - 专门用于日期维度的投资天数计算
2. **修改逻辑**：在 `processProjectDateEntry` 中使用混合计算方式：
   - 投资金额：使用完整历史数据（包括所有追加投资）
   - 当前价值：使用时间范围内的最新数据
   - 投资天数：从项目开始到时间范围内最新日期

## 修改内容

### 1. 新增 calculateInvestDaysForDateView 函数
```javascript
// 计算日期维度的投资天数（使用项目完整历史的第一个日期）
calculateInvestDaysForDateView: (project, currentDate) => {
    // 获取项目的所有balance记录
    // 按日期排序，获取最早的记录
    // 计算从最早日期到当前日期的天数
}
```

### 2. 修改 processProjectDateEntry 函数
- 获取完整历史数据用于计算初始投资金额
- 使用时间范围限制的历史数据来计算当前状态
- 使用新的投资天数计算逻辑
- 使用混合USDT金额计算逻辑

### 3. 增强错误处理
- 当时间范围内没有数据时返回 null
- 在调用处正确处理 null 返回值

## 预期效果

修复后，日期视图的日粒度应该显示：
1. **正确的总收益**：从项目开始到当前日期的累计收益
2. **正确的投资天数**：从项目第一个记录日期到当前日期的实际天数
3. **准确的APR计算**：基于正确的投资天数和收益金额

## 测试验证

可以通过以下方式验证修复效果：
1. 切换到日期维度视图
2. 选择日粒度
3. 查看项目的总收益和投资天数是否合理
4. 对比风险维度视图的数据，确保逻辑一致性

## 技术细节

### 投资天数计算原则
- **风险维度**：使用项目完整历史数据的第一个和最后一个日期
- **日期维度**：使用项目完整历史数据的第一个日期 + 时间范围内的最新日期

### USDT金额计算原则
- **投资金额**：始终使用完整历史数据（包括所有追加投资）
- **当前价值**：使用时间范围内最新记录的余额和对应日期的价格

这样确保了数据的准确性和一致性，同时满足了不同维度视图的需求。

## 修复完成状态

✅ **已完成的修改**：
1. 在 `DashboardBaseCalculator` 中新增 `calculateInvestDaysForDateView` 函数（第571-610行）
2. 重构 `processProjectDateEntry` 函数，使用混合计算逻辑（第1368-1454行）
3. 增强错误处理，正确处理时间范围内无数据的情况（第1286-1306行）

✅ **修复的核心问题**：
- 日粒度总收益显示为0的问题
- 日粒度投资天数显示为1天的问题
- 投资天数计算不受当前统计维度影响的需求

## 代码变更详情

### 变更1：新增投资天数计算函数
**位置**：第571-610行
**功能**：专门用于日期维度的投资天数计算，始终使用项目的第一个日期作为开始投资日期

### 变更2：重构项目日期条目处理函数
**位置**：第1368-1454行
**主要改进**：
- 分别获取完整历史数据和时间范围数据
- 使用新的投资天数计算逻辑
- 实现混合USDT金额计算（投资金额用完整数据，当前价值用时间范围数据）
- 增强空值处理

### 变更3：增强调用处的错误处理
**位置**：第1286-1306行
**改进**：正确处理 `processProjectDateEntry` 返回 null 的情况

## 技术实现亮点

1. **保持向后兼容**：修改不影响风险维度视图的现有逻辑
2. **数据准确性**：确保投资天数和收益计算的准确性
3. **错误处理**：完善的边界情况处理
4. **性能优化**：避免不必要的重复计算

## 验证建议

建议在以下场景下测试修复效果：
1. 单天记录的项目
2. 多天记录的项目
3. 有追加投资的项目
4. 不同代币单位的项目
5. 跨月/跨年的项目数据

修复后的日期视图应该能够正确显示每个项目从开始投资到指定日期的累计表现。

## 🔄 第二轮优化：时间维度收益计算

### 用户反馈
用户希望总收益列能够在不同的日、周、月、年维度视图中，只统计当前维度的总收益：
- 日维度：显示当日收益变化
- 周维度：显示当周收益变化
- 月维度：显示当月收益变化
- 年维度：显示当年收益变化

其他列（投资天数、日收益、当前APR等）保持累计计算方式。

### 新增功能：`calculateTimeRangeEarnings`
**位置**：第688-805行
**功能**：计算指定时间范围内的收益变化

**核心逻辑**：
1. 找到时间范围开始前的最近记录作为起始余额
2. 找到时间范围内的最后记录作为结束余额
3. 计算时间范围内的追加投资（add字段）
4. 净收益 = 结束余额 - 起始余额 - 追加投资
5. 转换为USDT价值

**边界情况处理**：
- 如果时间范围开始前没有记录：项目在此时间范围内开始，使用第一条记录计算
- 如果时间范围内没有记录：收益为0
- 如果价格数据缺失：返回-1并标记价格未找到

### 修改内容：`processProjectDateEntry`
**位置**：第1546-1579行
**改进**：
- 新增时间范围收益计算调用
- 总收益列使用时间范围收益（`timeRangeEarnings.timeRangeEarnings`）
- 其他列保持原有累计计算逻辑

### 预期效果对比

| 列名 | 修改前 | 修改后 |
|------|--------|--------|
| **总收益** | 从项目开始的累计收益 | 当前时间维度的收益变化 |
| **投资天数** | 时间范围内的天数（错误） | 从项目开始到当前的总天数（正确） |
| **日收益** | 基于时间范围的计算 | 基于总投资天数的平均日收益 |
| **当前APR** | 基于时间范围的计算 | 基于总投资天数和累计收益的年化收益率 |

### 示例场景

假设某项目：
- 1月1日：投资1000 USDT
- 1月15日：余额1050 USDT
- 2月1日：余额1100 USDT
- 2月15日：追加投资500 USDT，余额1600 USDT
- 2月28日：余额1650 USDT

在2月份月维度视图中：
- **总收益**：1650 - 1100 - 500 = 50 USDT（2月份的净收益）
- **投资天数**：58天（从1月1日到2月28日）
- **日收益**：150/58 ≈ 2.59 USDT/天（基于累计收益）
- **当前APR**：基于58天和150 USDT累计收益计算

这样用户就能清楚地看到每个时间维度的具体收益表现，同时保持其他指标的准确性。
