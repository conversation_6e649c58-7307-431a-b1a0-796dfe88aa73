# 日期视图修复测试

## 问题描述

在日期视图的日粒度中发现以下问题：
1. 总收益都显示为 0
2. 投资天数都显示为 1 天

## 问题根源分析

### 原始问题
- `calculateProjectHistoryInTimeRange` 函数严格按时间范围过滤数据
- 对于日粒度，时间范围就是单天（startDate = endDate）
- 如果某天只有一条记录，firstEntry 和 lastEntry 是同一条记录
- 导致：总收益 = lastBalance - firstBalance = 0，投资天数 = 1

### 修复方案
1. **新增函数**：`calculateInvestDaysForDateView` - 专门用于日期维度的投资天数计算
2. **修改逻辑**：在 `processProjectDateEntry` 中使用混合计算方式：
   - 投资金额：使用完整历史数据（包括所有追加投资）
   - 当前价值：使用时间范围内的最新数据
   - 投资天数：从项目开始到时间范围内最新日期

## 修改内容

### 1. 新增 calculateInvestDaysForDateView 函数
```javascript
// 计算日期维度的投资天数（使用项目完整历史的第一个日期）
calculateInvestDaysForDateView: (project, currentDate) => {
    // 获取项目的所有balance记录
    // 按日期排序，获取最早的记录
    // 计算从最早日期到当前日期的天数
}
```

### 2. 修改 processProjectDateEntry 函数
- 获取完整历史数据用于计算初始投资金额
- 使用时间范围限制的历史数据来计算当前状态
- 使用新的投资天数计算逻辑
- 使用混合USDT金额计算逻辑

### 3. 增强错误处理
- 当时间范围内没有数据时返回 null
- 在调用处正确处理 null 返回值

## 预期效果

修复后，日期视图的日粒度应该显示：
1. **正确的总收益**：从项目开始到当前日期的累计收益
2. **正确的投资天数**：从项目第一个记录日期到当前日期的实际天数
3. **准确的APR计算**：基于正确的投资天数和收益金额

## 测试验证

可以通过以下方式验证修复效果：
1. 切换到日期维度视图
2. 选择日粒度
3. 查看项目的总收益和投资天数是否合理
4. 对比风险维度视图的数据，确保逻辑一致性

## 技术细节

### 投资天数计算原则
- **风险维度**：使用项目完整历史数据的第一个和最后一个日期
- **日期维度**：使用项目完整历史数据的第一个日期 + 时间范围内的最新日期

### USDT金额计算原则
- **投资金额**：始终使用完整历史数据（包括所有追加投资）
- **当前价值**：使用时间范围内最新记录的余额和对应日期的价格

这样确保了数据的准确性和一致性，同时满足了不同维度视图的需求。

## 修复完成状态

✅ **已完成的修改**：
1. 在 `DashboardBaseCalculator` 中新增 `calculateInvestDaysForDateView` 函数（第571-610行）
2. 重构 `processProjectDateEntry` 函数，使用混合计算逻辑（第1368-1454行）
3. 增强错误处理，正确处理时间范围内无数据的情况（第1286-1306行）

✅ **修复的核心问题**：
- 日粒度总收益显示为0的问题
- 日粒度投资天数显示为1天的问题
- 投资天数计算不受当前统计维度影响的需求

## 代码变更详情

### 变更1：新增投资天数计算函数
**位置**：第571-610行
**功能**：专门用于日期维度的投资天数计算，始终使用项目的第一个日期作为开始投资日期

### 变更2：重构项目日期条目处理函数
**位置**：第1368-1454行
**主要改进**：
- 分别获取完整历史数据和时间范围数据
- 使用新的投资天数计算逻辑
- 实现混合USDT金额计算（投资金额用完整数据，当前价值用时间范围数据）
- 增强空值处理

### 变更3：增强调用处的错误处理
**位置**：第1286-1306行
**改进**：正确处理 `processProjectDateEntry` 返回 null 的情况

## 技术实现亮点

1. **保持向后兼容**：修改不影响风险维度视图的现有逻辑
2. **数据准确性**：确保投资天数和收益计算的准确性
3. **错误处理**：完善的边界情况处理
4. **性能优化**：避免不必要的重复计算

## 验证建议

建议在以下场景下测试修复效果：
1. 单天记录的项目
2. 多天记录的项目
3. 有追加投资的项目
4. 不同代币单位的项目
5. 跨月/跨年的项目数据

修复后的日期视图应该能够正确显示每个项目从开始投资到指定日期的累计表现。
